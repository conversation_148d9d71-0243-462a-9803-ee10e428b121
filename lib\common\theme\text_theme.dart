import 'package:flutter/material.dart';

TextTheme textTheme = const TextTheme(
  // Display
  displayLarge: TextStyle(fontSize: 40.0, fontWeight: FontWeight.w400, fontFamily: "tajwal"),
  displayMedium: TextStyle(fontSize: 35.0, fontWeight: FontWeight.w400, fontFamily: "tajwal"),
  displaySmall: TextStyle(fontSize: 30.0, fontWeight: FontWeight.w400, fontFamily: "tajwal"),

  // Headline
  headlineLarge: TextStyle(fontSize: 25, fontWeight: FontWeight.w400, fontFamily: "tajwal"),
  headlineMedium: TextStyle(fontSize: 23.0, fontWeight: FontWeight.w400, fontFamily: "tajwal"),
  headlineSmall: TextStyle(fontSize: 21.0, fontWeight: FontWeight.w200, fontFamily: "tajwal"),

  // Title
  titleLarge: TextStyle(fontSize: 22.0, fontWeight: FontWeight.w800, fontFamily: "tajwal"),
  titleMedium: TextStyle(fontSize: 20.0, fontWeight: FontWeight.w700, fontFamily: "tajwal"),
  titleSmall: TextStyle(fontSize: 18.0, fontWeight: FontWeight.w500, fontFamily: "tajwal"),

  // Body
  bodyLarge: TextStyle(fontSize: 17.0, fontWeight: FontWeight.w500, fontFamily: "tajwal"),
  bodyMedium: TextStyle(fontSize: 16.0, fontWeight: FontWeight.w400, fontFamily: "tajwal"),
  bodySmall: TextStyle(fontSize: 15.0, fontWeight: FontWeight.w400, fontFamily: "tajwal"),

  // Label
  labelLarge: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w300, fontFamily: "tajwal"),
  labelMedium: TextStyle(fontSize: 12.0, fontWeight: FontWeight.w300, fontFamily: "tajwal"),
  labelSmall: TextStyle(fontSize: 10.0, fontWeight: FontWeight.w300, fontFamily: "tajwal"),
);
