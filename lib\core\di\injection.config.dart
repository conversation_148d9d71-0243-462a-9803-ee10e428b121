// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i1;
import 'package:in_app_purchase/in_app_purchase.dart' as _i7;
import 'package:injectable/injectable.dart' as _i2;
import 'package:isar/isar.dart' as _i8;

import '../../features/home/<USER>/datasources/home_remote_datasource.dart'
    as _i31;
import '../../features/home/<USER>/datasources/local_food_data_source.dart'
    as _i9;
import '../../features/home/<USER>/repositories/exercise_repository_impl.dart'
    as _i30;
import '../../features/home/<USER>/repositories/food_repository_impl.dart'
    as _i28;
import '../../features/home/<USER>/repositories/home_repository_impl.dart'
    as _i33;
import '../../features/home/<USER>/repositories/exercise_repository.dart'
    as _i29;
import '../../features/home/<USER>/repositories/food_repository.dart' as _i27;
import '../../features/home/<USER>/repositories/home_repository.dart' as _i32;
import '../../features/home/<USER>/usecases/delete_exercise_usecase.dart'
    as _i43;
import '../../features/home/<USER>/usecases/get_daily_exercises_usecase.dart'
    as _i46;
import '../../features/home/<USER>/usecases/get_daily_user_data_usecase.dart'
    as _i47;
import '../../features/home/<USER>/usecases/update_daily_user_data_usecase.dart'
    as _i42;
import '../../features/home/<USER>/bloc/nutrition_bloc/bloc/nutrition_bloc.dart'
    as _i55;
import '../../features/home/<USER>/bloc/recent_activity_bloc.dart'
    as _i58;
import '../../features/main/data/repo/main_repo_impl.dart' as _i35;
import '../../features/main/domain/repo/main_repo.dart' as _i34;
import '../../features/main/presentation/bloc/main_bloc.dart' as _i54;
import '../../features/onboarding/data/datasource/onboarding_remote_datasource.dart'
    as _i12;
import '../../features/onboarding/data/repositories/onboarding_repository_imp.dart'
    as _i37;
import '../../features/onboarding/domain/repositories/onboarding_repository.dart'
    as _i36;
import '../../features/onboarding/domain/usecases/get_user_local_user_case.dart'
    as _i51;
import '../../features/onboarding/domain/usecases/local_delete_clear_user_use_case.dart'
    as _i52;
import '../../features/onboarding/domain/usecases/local_save_update_user_use_case.dart'
    as _i53;
import '../../features/onboarding/domain/usecases/submit_onboarding_usecase.dart'
    as _i41;
import '../../features/onboarding/presentation/bloc/onboarding_bloc.dart'
    as _i56;
import '../../features/quick_actions/exercise/data/datasources/exercise_local_datasource.dart'
    as _i22;
import '../../features/quick_actions/exercise/data/datasources/exercise_remote_datasource.dart'
    as _i23;
import '../../features/quick_actions/exercise/data/repositories/exercise_repository_impl.dart'
    as _i25;
import '../../features/quick_actions/exercise/domain/repositories/exercise_repository.dart'
    as _i24;
import '../../features/quick_actions/exercise/domain/services/calorie_calculation_service.dart'
    as _i4;
import '../../features/quick_actions/exercise/domain/usecases/save_exercise_ai_usecase.dart'
    as _i38;
import '../../features/quick_actions/exercise/domain/usecases/save_exercise_usecase.dart'
    as _i39;
import '../../features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart'
    as _i63;
import '../../features/quick_actions/food_database/data/datasources/food_remote_data_source.dart'
    as _i26;
import '../../features/quick_actions/food_database/data/datasources/local_food_database_data_source.dart'
    as _i10;
import '../../features/quick_actions/food_database/data/repositories/food_database_repository_impl.dart'
    as _i45;
import '../../features/quick_actions/food_database/domain/repositories/food_database_repository.dart'
    as _i44;
import '../../features/quick_actions/food_database/domain/usecases/add_selected_food_usecase.dart'
    as _i3;
import '../../features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart'
    as _i61;
import '../../features/quick_actions/food_database/domain/usecases/delete_meal_usecase.dart'
    as _i62;
import '../../features/quick_actions/food_database/domain/usecases/get_database_food_usecase.dart'
    as _i48;
import '../../features/quick_actions/food_database/domain/usecases/get_my_meals_usecase.dart'
    as _i49;
import '../../features/quick_actions/food_database/domain/usecases/get_recent_food_usecase.dart'
    as _i50;
import '../../features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart'
    as _i57;
import '../../features/quick_actions/food_database/domain/usecases/remove_selected_food_usecase.dart'
    as _i13;
import '../../features/quick_actions/food_database/domain/usecases/save_and_create_meal_usecase.dart'
    as _i64;
import '../../features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart'
    as _i60;
import '../../features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart'
    as _i65;
import '../../features/quick_actions/scan_food/data/datasources/scan_food_remote_datasource.dart'
    as _i15;
import '../../features/quick_actions/scan_food/data/repositories/scan_food_repository_impl.dart'
    as _i17;
import '../../features/quick_actions/scan_food/domain/repositories/scan_food_repository.dart'
    as _i16;
import '../../features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart'
    as _i21;
import '../../features/quick_actions/scan_food/domain/usecases/scan_barcode_use_case.dart'
    as _i40;
import '../../features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart'
    as _i59;
import '../../features/subscription_temp/presentation/bloc/subscription_bloc.dart'
    as _i19;
import '../../features/subscription_temp/services/revenue_cat_service.dart'
    as _i14;
import '../datasources/streak_local_data_source.dart' as _i18;
import '../datasources/user_local_data_source.dart' as _i20;
import '../isar_initialization.dart' as _i66;
import '../local_models/daily_data_model/daily_user_info_service.dart' as _i5;
import '../network/http_client.dart' as _i6;
import '../network/interceptor.dart' as _i11;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i1.GetIt> init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i3.AddSelectedFoodUseCase>(() => _i3.AddSelectedFoodUseCase());
    gh.factory<_i4.CalorieCalculationService>(
        () => _i4.CalorieCalculationService());
    gh.lazySingleton<_i5.DailyUserInfoService>(
        () => _i5.DailyUserInfoService());
    gh.lazySingleton<_i6.HTTPClient>(() => _i6.DioClient());
    gh.lazySingleton<_i7.InAppPurchase>(() => registerModule.inAppPurchase);
    await gh.factoryAsync<_i8.Isar>(
      () => registerModule.isar,
      preResolve: true,
    );
    gh.factory<_i9.LocalFoodDataSource>(
        () => _i9.LocalFoodDataSource(gh<_i8.Isar>()));
    gh.factory<_i10.LocalFoodDatabaseDataSource>(
        () => _i10.LocalFoodDatabaseDataSource(gh<_i8.Isar>()));
    gh.lazySingleton<_i11.LoggerInterceptor>(() => _i11.LoggerInterceptor());
    gh.lazySingleton<_i12.OnboardingRemoteDatasource>(() =>
        _i12.OnboardingRemoteDatasource(httpClient: gh<_i6.HTTPClient>()));
    gh.factory<_i13.RemoveSelectedFoodUseCase>(
        () => _i13.RemoveSelectedFoodUseCase());
    gh.factory<_i14.RevenueCatService>(() => _i14.RevenueCatService());
    gh.lazySingleton<_i15.ScanFoodRemoteDataSource>(
        () => _i15.ScanFoodRemoteDataSource(httpClient: gh<_i6.HTTPClient>()));
    gh.lazySingleton<_i16.ScanFoodRepository>(
        () => _i17.ScanFoodRepositoryImpl(gh<_i15.ScanFoodRemoteDataSource>()));
    gh.factory<_i18.StreakLocalDataSource>(
        () => _i18.StreakLocalDataSource(gh<_i8.Isar>()));
    gh.factory<_i19.SubscriptionBloc>(
        () => _i19.SubscriptionBloc(gh<_i14.RevenueCatService>()));
    gh.factory<_i20.UserLocalDataSource>(
        () => _i20.UserLocalDataSource(gh<_i8.Isar>()));
    gh.lazySingleton<_i21.AnalyzeFoodUseCase>(
        () => _i21.AnalyzeFoodUseCase(gh<_i16.ScanFoodRepository>()));
    gh.factory<_i22.ExerciseLocalDataSource>(
        () => _i22.ExerciseLocalDataSourceImpl(gh<_i8.Isar>()));
    gh.factory<_i23.ExerciseRemoteDataSource>(() =>
        _i23.ExerciseRemoteDataSourceImpl(httpClient: gh<_i6.HTTPClient>()));
    gh.factory<_i24.ExerciseRepository>(() => _i25.ExerciseRepositoryImpl(
          remoteDataSource: gh<_i23.ExerciseRemoteDataSource>(),
          localDataSource: gh<_i22.ExerciseLocalDataSource>(),
        ));
    gh.lazySingleton<_i26.FoodRemoteDataSource>(
        () => _i26.FoodRemoteDataSource(dioClient: gh<_i6.HTTPClient>()));
    gh.factory<_i27.FoodRepository>(() => _i28.FoodRepositoryImpl(
        localDataSource: gh<_i9.LocalFoodDataSource>()));
    gh.factory<_i29.HomeExerciseRepository>(() =>
        _i30.HomeExerciseRepositoryImpl(
            exerciseLocalDataSource: gh<_i22.ExerciseLocalDataSource>()));
    gh.lazySingleton<_i31.HomeLocalDataSource>(
        () => _i31.HomeLocalDataSourceImpl(isar: gh<_i8.Isar>()));
    gh.lazySingleton<_i32.HomeRepository>(() => _i33.HomeRepositoryImpl(
        localDataSource: gh<_i31.HomeLocalDataSource>()));
    gh.factory<_i34.MainRepo>(() =>
        _i35.MainRepoImpl(localDataSource: gh<_i18.StreakLocalDataSource>()));
    gh.lazySingleton<_i36.OnboardingRepository>(
        () => _i37.OnboardingRepositoryImp(
              onboardingRemoteDatasource: gh<_i12.OnboardingRemoteDatasource>(),
              userLocalDataSource: gh<_i20.UserLocalDataSource>(),
            ));
    gh.factory<_i38.SaveExerciseAiUseCase>(
        () => _i38.SaveExerciseAiUseCase(gh<_i24.ExerciseRepository>()));
    gh.factory<_i39.SaveExerciseUseCase>(
        () => _i39.SaveExerciseUseCase(gh<_i24.ExerciseRepository>()));
    gh.lazySingleton<_i40.ScanBarcodeUseCase>(() => _i40.ScanBarcodeUseCase(
        scanFoodRepository: gh<_i16.ScanFoodRepository>()));
    gh.lazySingleton<_i41.SubmitOnboardingUsecase>(() =>
        _i41.SubmitOnboardingUsecase(
            onboardingRepository: gh<_i36.OnboardingRepository>()));
    gh.factory<_i42.UpdateDailyUserDataUseCase>(() =>
        _i42.UpdateDailyUserDataUseCase(repository: gh<_i32.HomeRepository>()));
    gh.factory<_i43.DeleteExerciseUseCase>(
        () => _i43.DeleteExerciseUseCase(gh<_i29.HomeExerciseRepository>()));
    gh.factory<_i44.FoodDatabaseRepository>(
        () => _i45.FoodDatabaseRepositoryImpl(
              gh<_i26.FoodRemoteDataSource>(),
              localDataSource: gh<_i10.LocalFoodDatabaseDataSource>(),
            ));
    gh.factory<_i46.GetDailyExercisesUseCase>(
        () => _i46.GetDailyExercisesUseCase(gh<_i29.HomeExerciseRepository>()));
    gh.factory<_i47.GetDailyUserDataUseCase>(() =>
        _i47.GetDailyUserDataUseCase(repository: gh<_i32.HomeRepository>()));
    gh.factory<_i48.GetDatabaseFoodUsecase>(() => _i48.GetDatabaseFoodUsecase(
        repository: gh<_i44.FoodDatabaseRepository>()));
    gh.factory<_i49.GetMyMealsUsecase>(() =>
        _i49.GetMyMealsUsecase(repository: gh<_i44.FoodDatabaseRepository>()));
    gh.factory<_i50.GetRecentFoodUsecase>(() => _i50.GetRecentFoodUsecase(
        repository: gh<_i44.FoodDatabaseRepository>()));
    gh.lazySingleton<_i51.GetUserLocalUserCase>(() => _i51.GetUserLocalUserCase(
        onboardingRepository: gh<_i36.OnboardingRepository>()));
    gh.lazySingleton<_i52.LocalDeleteClearUserUseCase>(() =>
        _i52.LocalDeleteClearUserUseCase(
            onboardingRepository: gh<_i36.OnboardingRepository>()));
    gh.lazySingleton<_i53.LocalSaveUpdateUserUseCase>(() =>
        _i53.LocalSaveUpdateUserUseCase(
            onboardingRepository: gh<_i36.OnboardingRepository>()));
    gh.factory<_i54.MainBloc>(() => _i54.MainBloc(gh<_i34.MainRepo>()));
    gh.factory<_i55.NutritionBloc>(() => _i55.NutritionBloc(
        getDailyUserDataUseCase: gh<_i47.GetDailyUserDataUseCase>()));
    gh.factory<_i56.OnboardingBloc>(() => _i56.OnboardingBloc(
          submitOnboardingUsecase: gh<_i41.SubmitOnboardingUsecase>(),
          localDeleteClearUserUseCase: gh<_i52.LocalDeleteClearUserUseCase>(),
          localSaveUpdateUserUseCase: gh<_i53.LocalSaveUpdateUserUseCase>(),
          getUserLocalUserCase: gh<_i51.GetUserLocalUserCase>(),
        ));
    gh.lazySingleton<_i57.PostMealToLog>(() => _i57.PostMealToLog(
        foodDatabaseRepository: gh<_i44.FoodDatabaseRepository>()));
    gh.singleton<_i58.RecentActivityBloc>(() => _i58.RecentActivityBloc(
          foodRepository: gh<_i27.FoodRepository>(),
          exerciseRepository: gh<_i29.HomeExerciseRepository>(),
          getDailyUserDataUseCase: gh<_i47.GetDailyUserDataUseCase>(),
          updateDailyUserDataUseCase: gh<_i42.UpdateDailyUserDataUseCase>(),
          getDailyExercisesUseCase: gh<_i46.GetDailyExercisesUseCase>(),
          deleteExerciseUseCase: gh<_i43.DeleteExerciseUseCase>(),
        ));
    gh.factory<_i59.ScanFoodBloc>(() => _i59.ScanFoodBloc(
          analyzeFoodUseCase: gh<_i21.AnalyzeFoodUseCase>(),
          scanBarcodeUseCase: gh<_i40.ScanBarcodeUseCase>(),
          recentActivityBloc: gh<_i58.RecentActivityBloc>(),
        ));
    gh.lazySingleton<_i60.SearchMealsUseCase>(() => _i60.SearchMealsUseCase(
        foodDatabaseRepository: gh<_i44.FoodDatabaseRepository>()));
    gh.lazySingleton<_i61.CreateMealUseCase>(() => _i61.CreateMealUseCase(
        foodDatabaseRepository: gh<_i44.FoodDatabaseRepository>()));
    gh.factory<_i62.DeleteMealUseCase>(() =>
        _i62.DeleteMealUseCase(repository: gh<_i44.FoodDatabaseRepository>()));
    gh.factory<_i63.ExerciseBloc>(() => _i63.ExerciseBloc(
          saveExerciseUseCase: gh<_i39.SaveExerciseUseCase>(),
          saveExerciseAiUseCase: gh<_i38.SaveExerciseAiUseCase>(),
          recentActivityBloc: gh<_i58.RecentActivityBloc>(),
        ));
    gh.factory<_i64.SaveAndCreateMealUseCase>(
        () => _i64.SaveAndCreateMealUseCase(
              repository: gh<_i44.FoodDatabaseRepository>(),
              createMealUseCase: gh<_i61.CreateMealUseCase>(),
            ));
    gh.factory<_i65.FoodDatabaseBloc>(() => _i65.FoodDatabaseBloc(
          foodDatabaseRepository: gh<_i44.FoodDatabaseRepository>(),
          searchMealsUseCase: gh<_i60.SearchMealsUseCase>(),
          postMealToLog: gh<_i57.PostMealToLog>(),
          createMealUseCase: gh<_i61.CreateMealUseCase>(),
          addSelectedFoodUseCase: gh<_i3.AddSelectedFoodUseCase>(),
          removeSelectedFoodUseCase: gh<_i13.RemoveSelectedFoodUseCase>(),
          saveAndCreateMealUseCase: gh<_i64.SaveAndCreateMealUseCase>(),
          deleteMealUseCase: gh<_i62.DeleteMealUseCase>(),
        ));
    return this;
  }
}

class _$RegisterModule extends _i66.RegisterModule {}
