import 'package:flutter/material.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../../../common/extentions/colors_extension.dart';
import '../../../../common/widgets/app_gesture_detector.dart';

/// Widget for displaying a subscription package card
class SubscriptionPackageCard extends StatelessWidget {
  const SubscriptionPackageCard({
    super.key,
    required this.package,
    required this.onTap,
    this.isLoading = false,
  });

  final Package package;
  final VoidCallback onTap;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    final product = package.storeProduct;
    final isMonthly = _isMonthlyPackage();
    final isYearly = _isYearlyPackage();
    final isPopular = _isPopularPackage();

    return Stack(
      children: [
        AppGestureDetector(
          onTap: isLoading ? null : onTap,
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: context.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isPopular 
                    ? context.primaryColor 
                    : context.onSurface.withOpacity(0.2),
                width: isPopular ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row with title and price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getPackageTitle(isMonthly, isYearly),
                              style: context.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: context.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _getPackageSubtitle(isMonthly, isYearly),
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            product.priceString,
                            style: context.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: context.onSurface,
                            ),
                          ),
                          if (isYearly) ...[
                            const SizedBox(height: 2),
                            Text(
                              'Save 20%',
                              style: context.textTheme.bodySmall?.copyWith(
                                color: context.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),

                  // Intro price if available
                  if (product.introductoryPrice != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: context.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'Start with ${product.introductoryPrice!.priceString}',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Purchase button
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: isLoading ? null : onTap,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : Text(
                              'Subscribe Now',
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // Popular badge
        if (isPopular)
          Positioned(
            top: -1,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: context.primaryColor,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Text(
                'POPULAR',
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Check if this is a monthly package
  bool _isMonthlyPackage() {
    return package.identifier.toLowerCase().contains('monthly') ||
           package.identifier.toLowerCase().contains('month');
  }

  /// Check if this is a yearly package
  bool _isYearlyPackage() {
    return package.identifier.toLowerCase().contains('yearly') ||
           package.identifier.toLowerCase().contains('year') ||
           package.identifier.toLowerCase().contains('annual');
  }

  /// Check if this is a popular package (usually yearly)
  bool _isPopularPackage() {
    return _isYearlyPackage() || 
           package.identifier.toLowerCase().contains('popular');
  }

  /// Get package title based on identifier
  String _getPackageTitle(bool isMonthly, bool isYearly) {
    if (isMonthly) return 'Monthly Plan';
    if (isYearly) return 'Yearly Plan';
    return package.storeProduct.title;
  }

  /// Get package subtitle based on identifier
  String _getPackageSubtitle(bool isMonthly, bool isYearly) {
    if (isMonthly) return 'Billed monthly';
    if (isYearly) return 'Billed annually';
    return package.storeProduct.description;
  }
}
