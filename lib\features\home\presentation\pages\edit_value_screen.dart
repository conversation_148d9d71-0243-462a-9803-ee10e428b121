import 'dart:developer';

import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/metric_card.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum NutritionType {
  calories,
  protein,
  carbs,
  fat,
}

enum IngredientEditType {
  amount,
}

class EditValueScreen extends StatefulWidget {
  final FoodModel foodModel;
  final NutritionType nutritionType;
  final double targetValue;

  const EditValueScreen({
    super.key,
    required this.foodModel,
    required this.nutritionType,
    required this.targetValue,
  });

  @override
  State<EditValueScreen> createState() => _EditValueScreenState();
}

class _EditValueScreenState extends State<EditValueScreen> {
  late TextEditingController _valueController;
  late FocusNode _focusNode;
  bool _hasChanges = false;
  bool _isLoading = false;

  // Calculated nutrition values for real-time display
  int _calculatedCalories = 0;
  double _calculatedProtein = 0.0;
  double _calculatedCarbs = 0.0;
  double _calculatedFat = 0.0;

  // Macronutrient ratio constants
  static const double _carbsRatio = 0.50; // 50% of calories
  static const double _proteinRatio = 0.20; // 20% of calories
  static const double _fatRatio = 0.30; // 30% of calories
  static const double _carbsCaloriesPerGram = 4.0;
  static const double _proteinCaloriesPerGram = 4.0;
  static const double _fatCaloriesPerGram = 9.0;

  @override
  void initState() {
    super.initState();
    _valueController = TextEditingController(text: _getCurrentValue().toString());
    _focusNode = FocusNode();
    _valueController.addListener(_onValueChanged);

    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _formatValue();
      }
    });

    // Initialize calculated values with current food model values
    _calculatedCalories = widget.foodModel.calories ?? 0;
    _calculatedProtein = widget.foodModel.protein ?? 0.0;
    _calculatedCarbs = widget.foodModel.carbs ?? 0.0;
    _calculatedFat = widget.foodModel.fat ?? 0.0;

    // Auto-focus the text field when the screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  void _onValueChanged() {
    final newValue = double.tryParse(_valueController.text) ?? 0.0;
    final currentValue = _getCurrentValue();

    // Calculate nutrition values in real-time
    _calculateNutritionValues(newValue, widget.nutritionType);

    setState(() {
      _hasChanges = newValue != currentValue;
    });
  }

  double _getCurrentValue() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return widget.foodModel.calories?.toDouble() ?? 0.0;
      case NutritionType.protein:
        return widget.foodModel.protein ?? 0.0;
      case NutritionType.carbs:
        return widget.foodModel.carbs ?? 0.0;
      case NutritionType.fat:
        return widget.foodModel.fat ?? 0.0;
    }
  }

  /// Calculate nutrition values based on macronutrient ratios
  /// This maintains balanced macro distribution when editing individual nutrition values
  /// Note: This is separate from serving size scaling, which proportionally scales all values
  void _calculateNutritionValues(double inputValue, NutritionType inputType) {
    switch (inputType) {
      case NutritionType.calories:
        _calculateFromCalories(inputValue);
        break;
      case NutritionType.protein:
        _calculateFromProtein(inputValue);
        break;
      case NutritionType.carbs:
        _calculateFromCarbs(inputValue);
        break;
      case NutritionType.fat:
        _calculateFromFat(inputValue);
        break;
    }
  }

  /// Calculate all macronutrients from calories
  void _calculateFromCalories(double calories) {
    _calculatedCalories = calories.round();
    _calculatedCarbs = (calories * _carbsRatio) / _carbsCaloriesPerGram;
    _calculatedProtein = (calories * _proteinRatio) / _proteinCaloriesPerGram;
    _calculatedFat = (calories * _fatRatio) / _fatCaloriesPerGram;
  }

  /// Calculate calories and other macronutrients from protein
  void _calculateFromProtein(double protein) {
    _calculatedProtein = protein;
    // Calculate total calories from protein (protein should be 20% of total calories)
    final totalCalories = (protein * _proteinCaloriesPerGram) / _proteinRatio;
    _calculatedCalories = totalCalories.round();
    _calculatedCarbs = (totalCalories * _carbsRatio) / _carbsCaloriesPerGram;
    _calculatedFat = (totalCalories * _fatRatio) / _fatCaloriesPerGram;
  }

  /// Calculate calories and other macronutrients from carbs
  void _calculateFromCarbs(double carbs) {
    _calculatedCarbs = carbs;
    // Calculate total calories from carbs (carbs should be 50% of total calories)
    final totalCalories = (carbs * _carbsCaloriesPerGram) / _carbsRatio;
    _calculatedCalories = totalCalories.round();
    _calculatedProtein = (totalCalories * _proteinRatio) / _proteinCaloriesPerGram;
    _calculatedFat = (totalCalories * _fatRatio) / _fatCaloriesPerGram;
  }

  /// Calculate calories and other macronutrients from fat
  void _calculateFromFat(double fat) {
    _calculatedFat = fat;
    // Calculate total calories from fat (fat should be 30% of total calories)
    final totalCalories = (fat * _fatCaloriesPerGram) / _fatRatio;
    _calculatedCalories = totalCalories.round();
    _calculatedCarbs = (totalCalories * _carbsRatio) / _carbsCaloriesPerGram;
    _calculatedProtein = (totalCalories * _proteinRatio) / _proteinCaloriesPerGram;
  }

  String _getNutritionTitle() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return LocaleKeys.common_calories.tr();
      case NutritionType.protein:
        return LocaleKeys.common_protien.tr();
      case NutritionType.carbs:
        return LocaleKeys.common_carbs.tr();
      case NutritionType.fat:
        return LocaleKeys.common_fat.tr();
    }
  }

  String _getNutritionUnit() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return 'kcal';
      case NutritionType.protein:
      case NutritionType.carbs:
      case NutritionType.fat:
        return 'g';
    }
  }

  String _getNutritionIcon() {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return Assets.imagesCals;
      case NutritionType.protein:
        return Assets.imagesProtien;
      case NutritionType.carbs:
        return Assets.imagesCarbs;
      case NutritionType.fat:
        return Assets.imagesFats;
    }
  }

  Color _getNutritionColor(BuildContext context) {
    switch (widget.nutritionType) {
      case NutritionType.calories:
        return context.primaryColor;
      case NutritionType.protein:
        return const Color(0xFFE55B35);
      case NutritionType.carbs:
        return const Color(0xFFFFA76E);
      case NutritionType.fat:
        return const Color(0xFF4277ff);
    }
  }

  void _saveChanges() async {
    if (!_hasChanges) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newValue = double.tryParse(_valueController.text) ?? 0.0;

      // Calculate all nutrition values based on macronutrient ratios
      _calculateNutritionValues(newValue, widget.nutritionType);

      // Get original values for difference calculation
      final originalCalories = widget.foodModel.calories?.toDouble() ?? 0.0;
      final originalProtein = widget.foodModel.protein ?? 0.0;
      final originalCarbs = widget.foodModel.carbs ?? 0.0;
      final originalFat = widget.foodModel.fat ?? 0.0;

      // Calculate differences for all nutrition values
      final caloriesDiff = _calculatedCalories.toDouble() - originalCalories;
      final proteinDiff = _calculatedProtein - originalProtein;
      final carbsDiff = _calculatedCarbs - originalCarbs;
      final fatDiff = _calculatedFat - originalFat;

      // Create updated food model with all calculated values
      final updatedFoodModel = widget.foodModel.copyWith(
        calories: _calculatedCalories,
        protein: double.parse(_calculatedProtein.toStringAsFixed(1)),
        carbs: double.parse(_calculatedCarbs.toStringAsFixed(1)),
        fat: double.parse(_calculatedFat.toStringAsFixed(1)),
        serving: widget.foodModel.serving, // Preserve serving size
      );

      log('EditValueScreen: Updated food model date: ${updatedFoodModel.date}');
      log('EditValueScreen: Nutrition differences - Calories: $caloriesDiff, Protein: $proteinDiff, Carbs: $carbsDiff, Fat: $fatDiff');

      // Use the new event with calculated differences
      context.read<RecentActivityBloc>().add(UpdateFoodWithDifferences(
            meal: updatedFoodModel,
            caloriesDiff: caloriesDiff,
            carbsDiff: carbsDiff,
            proteinDiff: proteinDiff,
            fatDiff: fatDiff,
          ));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
    context.pop();
  }

  @override
  void dispose() {
    _valueController.dispose();
    _focusNode.dispose();

    super.dispose();
  }

  /// Build card showing calculated nutrition values
  Widget _buildCalculatedValuesCard() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: context.onPrimaryColor,
        border: Border.all(
          color: context.onSecondary.withAlpha(50),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.home_automatically_calculated_nutrition.tr(),
            style: context.textTheme.titleMedium!.copyWith(
              color: context.onSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            LocaleKeys.home_carbs_50_protein_20_fats_30.tr(),
            style: context.textTheme.labelLarge!.copyWith(
              color: context.onSecondary,
            ),
          ),
          const SizedBox(height: 18),
          SizedBox(
            width: double.infinity,
            child: Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              alignment: WrapAlignment.center,
              spacing: 12,
              runSpacing: 7,
              children: [
                _buildNutritionValueItem(
                  LocaleKeys.home_calories.tr(),
                  _calculatedCalories.toString(),
                  LocaleKeys.home_calorie.tr(),
                  context.primaryColor,
                ),
                _buildNutritionValueItem(
                  LocaleKeys.home_protein.tr(),
                  _calculatedProtein.toStringAsFixed(1),
                  LocaleKeys.home_grams.tr(),
                  const Color(0xFFE55B35),
                ),
                _buildNutritionValueItem(
                  LocaleKeys.home_carbs.tr(),
                  _calculatedCarbs.toStringAsFixed(1),
                  LocaleKeys.home_grams.tr(),
                  const Color(0xFFFFA76E),
                ),
                _buildNutritionValueItem(
                  LocaleKeys.home_fats.tr(),
                  _calculatedFat.toStringAsFixed(1),
                  LocaleKeys.home_grams.tr(),
                  const Color(0xFF4277ff),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual nutrition value item
  Widget _buildNutritionValueItem(String label, String value, String unit, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withAlpha(30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            softWrap: true,
            maxLines: 2,
            '$value $unit',
            style: context.textTheme.bodySmall!.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 5),
        Text(
          label,
          style: context.textTheme.labelMedium!.copyWith(color: context.onSecondary, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  void _formatValue() {
    final text = _valueController.text;
    final parsed = double.tryParse(text);
    if (parsed != null) {
      _valueController.text = parsed.toStringAsFixed(2).replaceAll(RegExp(r'\.?0+$'), '');
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentValue = double.tryParse(_valueController.text) ?? 0.0;
    final progress = calculateSafeProgress(widget.targetValue, currentValue);

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: context.background,
      appBar: AppBar(
        backgroundColor: context.background,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(
            Icons.arrow_back,
            color: context.primaryColor,
          ),
        ),
        title: Text(
          '${LocaleKeys.common_edit.tr()} ${_getNutritionTitle()}',
          style: context.textTheme.titleLarge!.copyWith(
            color: context.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        // actions: [
        //   if (_hasChanges)
        //     TextButton(
        //       onPressed: _isLoading ? null : _saveChanges,
        //       child: _isLoading
        //           ? const SizedBox(
        //               width: 20,
        //               height: 20,
        //               child: CircularProgressIndicator(strokeWidth: 2),
        //             )
        //           : Text(
        //               'حفظ',
        //               style: TextStyle(
        //                 color: context.primaryColor,
        //                 fontWeight: FontWeight.bold,
        //               ),
        //             ),
        //     ),
        // ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // MetricCard showing target vs current
              HorizontalMetricCard(
                title: currentValue.toInt().toString(),
                description: "",
                icon: _getNutritionIcon(),
                color: _getNutritionColor(context),
                progress: progress,
                progressHeight: 60,
                progressWidth: 60,
              ),

              const SizedBox(height: 15),

              // Text field for editing
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: context.onPrimaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(25),
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: _valueController,
                  focusNode: _focusNode,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                  ],
                  style: context.textTheme.headlineMedium!.copyWith(
                    color: _getNutritionColor(context),
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  onFieldSubmitted: (_) => _formatValue(),
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(
                        color: _getNutritionColor(context),
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 20,
                    ),
                    suffixText: _getNutritionUnit(),
                    suffixStyle: context.textTheme.titleLarge!.copyWith(
                      color: context.onSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                    hintText: 'أدخل القيمة',
                    hintStyle: context.textTheme.titleMedium!.copyWith(
                      color: context.onSecondary.withAlpha(100),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Calculated nutrition values display
              if (_hasChanges) _buildCalculatedValuesCard(),

              const Spacer(),

              // Save button (full width)
              if (_hasChanges)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveChanges,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getNutritionColor(context),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            LocaleKeys.home_save_changes.tr(),
                            style: context.textTheme.titleMedium!.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
