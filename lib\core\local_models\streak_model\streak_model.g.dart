// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'streak_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetStreakModelCollection on Isar {
  IsarCollection<StreakModel> get streakModels => this.collection();
}

const StreakModelSchema = CollectionSchema(
  name: r'StreakModel',
  id: -1150190822398184549,
  properties: {
    r'hasAction': PropertySchema(
      id: 0,
      name: r'hasAction',
      type: IsarType.bool,
    ),
    r'streakDate': PropertySchema(
      id: 1,
      name: r'streakDate',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _streakModelEstimateSize,
  serialize: _streakModelSerialize,
  deserialize: _streakModelDeserialize,
  deserializeProp: _streakModelDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _streakModelGetId,
  getLinks: _streakModelGetLinks,
  attach: _streakModelAttach,
  version: '3.1.0+1',
);

int _streakModelEstimateSize(
  StreakModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _streakModelSerialize(
  StreakModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeBool(offsets[0], object.hasAction);
  writer.writeDateTime(offsets[1], object.streakDate);
}

StreakModel _streakModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = StreakModel(
    hasAction: reader.readBool(offsets[0]),
    streakDate: reader.readDateTime(offsets[1]),
  );
  object.id = id;
  return object;
}

P _streakModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readBool(offset)) as P;
    case 1:
      return (reader.readDateTime(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _streakModelGetId(StreakModel object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _streakModelGetLinks(StreakModel object) {
  return [];
}

void _streakModelAttach(
    IsarCollection<dynamic> col, Id id, StreakModel object) {
  object.id = id;
}

extension StreakModelQueryWhereSort
    on QueryBuilder<StreakModel, StreakModel, QWhere> {
  QueryBuilder<StreakModel, StreakModel, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension StreakModelQueryWhere
    on QueryBuilder<StreakModel, StreakModel, QWhereClause> {
  QueryBuilder<StreakModel, StreakModel, QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterWhereClause> idGreaterThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterWhereClause> idLessThan(Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension StreakModelQueryFilter
    on QueryBuilder<StreakModel, StreakModel, QFilterCondition> {
  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition>
      hasActionEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hasAction',
        value: value,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition>
      streakDateEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'streakDate',
        value: value,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition>
      streakDateGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'streakDate',
        value: value,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition>
      streakDateLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'streakDate',
        value: value,
      ));
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterFilterCondition>
      streakDateBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'streakDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension StreakModelQueryObject
    on QueryBuilder<StreakModel, StreakModel, QFilterCondition> {}

extension StreakModelQueryLinks
    on QueryBuilder<StreakModel, StreakModel, QFilterCondition> {}

extension StreakModelQuerySortBy
    on QueryBuilder<StreakModel, StreakModel, QSortBy> {
  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> sortByHasAction() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasAction', Sort.asc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> sortByHasActionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasAction', Sort.desc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> sortByStreakDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streakDate', Sort.asc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> sortByStreakDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streakDate', Sort.desc);
    });
  }
}

extension StreakModelQuerySortThenBy
    on QueryBuilder<StreakModel, StreakModel, QSortThenBy> {
  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> thenByHasAction() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasAction', Sort.asc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> thenByHasActionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hasAction', Sort.desc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> thenByStreakDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streakDate', Sort.asc);
    });
  }

  QueryBuilder<StreakModel, StreakModel, QAfterSortBy> thenByStreakDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'streakDate', Sort.desc);
    });
  }
}

extension StreakModelQueryWhereDistinct
    on QueryBuilder<StreakModel, StreakModel, QDistinct> {
  QueryBuilder<StreakModel, StreakModel, QDistinct> distinctByHasAction() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hasAction');
    });
  }

  QueryBuilder<StreakModel, StreakModel, QDistinct> distinctByStreakDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'streakDate');
    });
  }
}

extension StreakModelQueryProperty
    on QueryBuilder<StreakModel, StreakModel, QQueryProperty> {
  QueryBuilder<StreakModel, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<StreakModel, bool, QQueryOperations> hasActionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hasAction');
    });
  }

  QueryBuilder<StreakModel, DateTime, QQueryOperations> streakDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'streakDate');
    });
  }
}
