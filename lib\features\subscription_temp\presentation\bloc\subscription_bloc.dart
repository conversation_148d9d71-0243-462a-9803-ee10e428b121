import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../services/revenue_cat_service.dart';

part 'subscription_event.dart';
part 'subscription_state.dart';

/// Simplified BLoC for managing subscription operations using RevenueCat
/// Directly uses RevenueCat service for core functionality
@injectable
class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final RevenueCatService _revenueCatService;

  SubscriptionBloc(this._revenueCatService) : super(const SubscriptionState()) {
    on<InitializeSubscription>(_onInitializeSubscription);
    on<LoadSubscriptionPackages>(_onLoadSubscriptionPackages);
    on<PurchaseSubscription>(_onPurchaseSubscription);
    on<RestoreSubscriptions>(_onRestoreSubscriptions);
    on<ResetSubscriptionState>(_onResetSubscriptionState);
  }

  /// Initialize the subscription service
  Future<void> _onInitializeSubscription(
    InitializeSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.loading, clearErrorMessage: true));

    try {
      await _revenueCatService.initialize(appUserId: event.appUserId);
      emit(state.copyWith(
        status: SubscriptionBlocStatus.initial,
        isInitialized: true,
        clearErrorMessage: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Load available subscription packages
  Future<void> _onLoadSubscriptionPackages(
    LoadSubscriptionPackages event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.loading, clearErrorMessage: true));

    try {
      final packages = await _revenueCatService.getAvailablePackages(
        offeringIdentifier: event.offeringIdentifier,
      );
      emit(state.copyWith(
        status: SubscriptionBlocStatus.packagesLoaded,
        packages: packages,
        clearErrorMessage: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Purchase a subscription package
  Future<void> _onPurchaseSubscription(
    PurchaseSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.purchasing, clearErrorMessage: true));

    try {
      final customerInfo = await _revenueCatService.purchasePackageById(event.packageIdentifier);
      emit(state.copyWith(
        status: SubscriptionBlocStatus.purchaseSuccess,
        customerInfo: customerInfo,
        clearErrorMessage: true,
      ));
    } catch (e) {
      // Handle purchase cancellation differently from other errors
      if (e.toString().contains('cancelled') || e.toString().contains('canceled')) {
        // Return to previous state without showing error
        emit(state.copyWith(
          status: state.packages.isNotEmpty
              ? SubscriptionBlocStatus.packagesLoaded
              : SubscriptionBlocStatus.initial,
          clearErrorMessage: true,
        ));
      } else {
        emit(state.copyWith(
          status: SubscriptionBlocStatus.error,
          errorMessage: e.toString(),
        ));
      }
    }
  }

  /// Restore previous purchases
  Future<void> _onRestoreSubscriptions(
    RestoreSubscriptions event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.loading, clearErrorMessage: true));

    try {
      final customerInfo = await _revenueCatService.restorePurchases();
      emit(state.copyWith(
        status: SubscriptionBlocStatus.restoreSuccess,
        customerInfo: customerInfo,
        clearErrorMessage: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Reset the subscription state
  void _onResetSubscriptionState(
    ResetSubscriptionState event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(const SubscriptionState());
  }
}
