import 'dart:developer';

import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/enums/scan_mode.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/managers/camera_controller_manager.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/managers/barcode_scanner_manager.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/widgets/camera_controllers_widget.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/widgets/camera_preview_widget.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/widgets/scan_action_button.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ScanCameraWidget extends StatefulWidget {
  const ScanCameraWidget({super.key});

  @override
  State<ScanCameraWidget> createState() => _ScanCameraWidgetState();
}

class _ScanCameraWidgetState extends State<ScanCameraWidget> with WidgetsBindingObserver {
  late final CameraControllerManager _cameraManager;
  late final BarcodeScannerManager _barcodeManager;

  bool _isLoading = true;
  String? _errorMessage;
  ScanMode _currentMode = ScanMode.scanFood;

  @override
  void initState() {
    super.initState();
    _cameraManager = CameraControllerManager();
    _barcodeManager = BarcodeScannerManager();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    log("Disposing ScanCameraWidget");
    _cameraManager.markAsDisposing();
    _barcodeManager.stopScanning();
    WidgetsBinding.instance.removeObserver(this);
    _cameraManager.disposeCamera();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    log("App lifecycle state changed: $state");

    if (_cameraManager.isDisposing) return;

    if (state == AppLifecycleState.inactive || state == AppLifecycleState.paused) {
      log("App going to background, disposing camera");
      _cameraManager.disposeCamera();
    } else if (state == AppLifecycleState.resumed) {
      log("App resumed, reinitializing camera");
      if (_cameraManager.controller == null && mounted && _currentMode.requiresCamera) {
        _initializeCamera();
      }
    }
  }

  Future<void> _initializeCamera() async {
    if (!_currentMode.requiresCamera) {
      log("Current mode doesn't require camera, skipping initialization");
      return;
    }

    setState(() => _isLoading = true);

    try {
      final hasPermission = await _cameraManager.checkAndRequestPermission();

      if (!hasPermission) {
        setState(() {
          _isLoading = false;
          _errorMessage = _cameraManager.hasPermission ? 'Camera permission is required' : 'Please enable camera permissions in settings';
        });
        return;
      }

      final error = await _cameraManager.initializeCamera();

      if (mounted && !_cameraManager.isDisposing) {
        setState(() {
          _isLoading = false;
          _errorMessage = error;
        });

        // Start continuous barcode scanning if in barcode mode
        if (_currentMode == ScanMode.barcode && error == null) {
          _startBarcodeScanning();
        }
      }
    } catch (e) {
      log("Error initializing camera: $e");
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to initialize camera: $e';
        });
      }
    }
  }

  void _startBarcodeScanning() {
    _barcodeManager.startContinuousBarcodeScan(
      cameraManager: _cameraManager,
      onBarcodeDetected: (barcode, imageFile) async {
        await _cameraManager.disposeCamera();
        if (mounted) {
          context.read<ScanFoodBloc>().add(ProcessCapturedImageEvent(
                imageFile: imageFile,
                isBarcode: true,
                barcode: barcode,
                isLabel: _currentMode == ScanMode.label,
              ));
          context.pop();
        }
      },
      onScanningStateChanged: () {
        if (mounted) setState(() {});
      },
    );
  }

  Future<void> _handleModeChange(ScanMode newMode) async {
    if (_currentMode == newMode) return;

    _barcodeManager.stopScanning();

    setState(() {
      _currentMode = newMode;
      _errorMessage = null;
    });

    if (newMode == ScanMode.gallery) {
      log("Gallery mode selected, disposing camera...");
      await _cameraManager.disposeCamera();
      if (mounted && !_cameraManager.isDisposing) {
        context.read<ScanFoodBloc>().add(PickImageFromGalleryEvent(context: context));
      }
    } else if (newMode.requiresCamera) {
      // Reinitialize camera if needed
      if (!_cameraManager.isInitialized && _cameraManager.hasPermission) {
        await _initializeCamera();
      } else if (_cameraManager.isInitialized && newMode == ScanMode.barcode) {
        _startBarcodeScanning();
      }
    }
  }

  Future<void> _captureImage() async {
    if (_cameraManager.controller == null || !_cameraManager.controller!.value.isInitialized || _cameraManager.isDisposing) {
      log("Cannot capture image: controller not ready or disposing");
      return;
    }

    try {
      log("Capturing image...");
      if (mounted && !_cameraManager.isDisposing) {
        setState(() => _isLoading = true);
      }

      final imageFile = await _cameraManager.captureImage();

      if (imageFile == null) {
        throw Exception("Failed to capture image");
      }

      // For barcode mode, try to scan the captured image
      if (_currentMode == ScanMode.barcode) {
        final barcode = await _barcodeManager.scanSingleImage(imageFile);
        if (barcode != null) {
          await _cameraManager.disposeCamera();
          if (mounted) {
            context.read<ScanFoodBloc>().add(ProcessCapturedImageEvent(
                  imageFile: imageFile,
                  isBarcode: true,
                  barcode: barcode,
                  isLabel: _currentMode == ScanMode.label,
                ));
            context.pop();
          }
          return;
        }
      }

      if (mounted && !_cameraManager.isDisposing) {
        setState(() => _isLoading = false);

        context.read<ScanFoodBloc>().add(ProcessCapturedImageEvent(
              imageFile: imageFile,
              isLabel: _currentMode == ScanMode.label,
            ));

        await _cameraManager.disposeCamera();

        if (mounted && context.canPop()) {
          context.pop();
        }
      }
    } catch (e) {
      log("Failed to capture image: $e");
      if (mounted && !_cameraManager.isDisposing) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to capture image: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Camera preview
          CameraPreviewWidget(
            controller: _cameraManager.controller,
            hasPermission: _cameraManager.hasPermission,
            errorMessage: _errorMessage,
            currentMode: _currentMode,
            onRetry: _initializeCamera,
          ),

          // Scanner frame overlay
          Transform.translate(
            offset: const Offset(0, -40),
            child: Padding(
              padding:
                  _currentMode == ScanMode.barcode ? const EdgeInsetsDirectional.symmetric(horizontal: 20) : EdgeInsetsDirectional.zero,
              child: Image.asset(
                _currentMode == ScanMode.barcode ? Assets.imagesBarcodeContainer : Assets.imagesCameraFrame,
                scale: 1.2,
              ),
            ),
          ),

          // Top bar with close button
          Positioned(
            top: 50,
            left: 16,
            right: 16,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CircleAvatar(
                  backgroundColor: Colors.white,
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.black),
                    onPressed: () async {
                      log("Close button tapped, disposing camera...");
                      await _cameraManager.disposeCamera();
                      if (context.mounted && context.canPop()) {
                        context.pop();
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          // Bottom controls
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // Action buttons row
                _buildActionButtons(),
                const SizedBox(height: 30),

                // Camera controls
                CameraControlsWidget(
                  isFlashOn: _cameraManager.isFlashOn,
                  canToggleFlash: _cameraManager.controller != null,
                  canCapture: _cameraManager.isInitialized && !_isLoading && _currentMode != ScanMode.gallery,
                  currentMode: _currentMode,
                  onToggleFlash: _cameraManager.toggleFlash,
                  onCapture: _captureImage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        spacing: 12,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          const SizedBox.shrink(),
          ScanActionButton(
            icon: Icons.photo,
            label: LocaleKeys.scan_gallery.tr(),
            isSelected: _currentMode == ScanMode.gallery,
            onTap: () => _handleModeChange(ScanMode.gallery),
          ),
          ScanActionButton(
            icon: Icons.info_outline,
            label: LocaleKeys.scan_food_label.tr(),
            isSelected: _currentMode == ScanMode.label,
            onTap: () => _handleModeChange(ScanMode.label),
          ),
          ScanActionButton(
            icon: Icons.qr_code_scanner,
            label: LocaleKeys.scan_barcode.tr(),
            isSelected: _currentMode == ScanMode.barcode,
            onTap: () => _handleModeChange(ScanMode.barcode),
          ),
          ScanActionButton(
            icon: Icons.camera_alt_outlined,
            label: LocaleKeys.scan_scan_food.tr(),
            isSelected: _currentMode == ScanMode.scanFood,
            onTap: () => _handleModeChange(ScanMode.scanFood),
          ),
        ],
      ),
    );
  }
}
