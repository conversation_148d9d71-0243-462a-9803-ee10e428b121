// lib/features/quick_actions/scan_food/presentation/widgets/camera_controls_widget.dart

import 'package:flutter/material.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/enums/scan_mode.dart';

class CameraControlsWidget extends StatelessWidget {
  final bool isFlashOn;
  final bool canToggleFlash;
  final bool canCapture;
  final ScanMode currentMode;
  final VoidCallback? onToggleFlash;
  final VoidCallback? onCapture;

  const CameraControlsWidget({
    super.key,
    required this.isFlashOn,
    required this.canToggleFlash,
    required this.canCapture,
    required this.currentMode,
    this.onToggleFlash,
    this.onCapture,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Flash toggle button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: CircleAvatar(
            backgroundColor: Colors.white,
            child: IconButton(
              icon: Icon(
                isFlashOn ? Icons.flash_on : Icons.flash_off,
                color: context.primaryColor,
              ),
              onPressed: canToggleFlash ? onToggleFlash : null,
            ),
          ),
        ),
        const Spacer(),

        // Capture button or placeholder for barcode mode
        _buildCaptureButton(context),

        const Spacer(),
        const SizedBox(width: 70),
      ],
    );
  }

  Widget _buildCaptureButton(BuildContext context) {
    if (currentMode == ScanMode.barcode) {
      // Invisible placeholder for barcode mode (continuous scanning)
      return Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.transparent,
          border: Border.all(color: Colors.transparent, width: 3),
        ),
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.transparent),
          ),
        ),
      );
    }

    // Regular capture button for other modes
    return AppGestureDetector(
      onTap: canCapture ? onCapture : null,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          border: Border.all(color: Colors.white, width: 3),
        ),
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: context.onPrimaryContainer),
          ),
        ),
      ),
    );
  }
}
