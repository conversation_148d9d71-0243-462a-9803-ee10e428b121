// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters, constant_identifier_names

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader{
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String,dynamic> _ar = {
  "common": {
    "ma": "ذكر",
    "fe": "أنثى",
    "months": {
      "january": "يناير",
      "february": "فبراير",
      "march": "مارس",
      "april": "أبريل",
      "may": "مايو",
      "june": "يونيو",
      "july": "يوليو",
      "august": "أغسطس",
      "september": "سبتمبر",
      "october": "أكتو<PERSON><PERSON>",
      "november": "نوفمبر",
      "december": "ديسمبر"
    },
    "select_language": "اختر لغة",
    "navbar": {
      "home": "الرئيسية",
      "settings": "الإعدادات",
      "profile": "الملف",
      "add": "إضافة"
    },
    "calories": "السعرات الحرارية",
    "carbs": "الكربوهيدرات",
    "protien": "البروتين",
    "fat": "الدهون",
    "servings": "الحصة",
    "halal": "حلال",
    "haram": "غير حلال",
    "health_score": "النتيجة الصحية",
    "add": "إضافة",
    "no_internet_connection": "لا يوجد اتصال بالإنترنت",
    "back": "الرجوع",
    "hour": "الساعة",
    "minute": "الدقيقة",
    "do_u_want_to_logout": "هل ترغب في تسجيل الخروج؟",
    "confirm": "تأكيد",
    "cancel": "إلغاء",
    "continue": "الاستمرار",
    "error_recognizing_food": "حدث خطأ أثناء تحديد الطعام، يرجى المحاولة مجددًا",
    "next": "التالي"
  },
  "onboarding": {
    "your_weight": "وزنك",
    "gram": "غ",
    "lets_start": "يلا نبدأ",
    "what_is_your_goal": "ما هو هدفك؟",
    "goal_weight_loss": "خسارة الوزن",
    "goal_maintenance": "المحافظة على الوزن",
    "goal_weight_gain": "زيادة الوزن",
    "one_photo": "صورة واحدة... وكل شيء بينحسب!",
    "one_photo_desc": "صوّر وجبتك، وخلي الذكاء الاصطناعي يحللها ويحسبلك السعرات والمغذيات بدقة.",
    "halal": "أعرف شو تأكل... وأعرف إذا حلال!",
    "halal_desc": "التطبيق يتميز بتحديد الأطعمة الحلال من الحرام تلقائيًا، وبيطمنك على أكلك وين ما كنت.",
    "health": "صحتك أهم... وهدفك أسهل",
    "health_desc": "تابع سعراتك، نظم أكلك، واحصل على خطة غذائية مدروسة وسهلة التطبيق.",
    "start": "ابدأ",
    "next": "التالي",
    "choose_your_gender": "حدد جنسك",
    "we_will_use_this_information_to_personalize_your_plan": "سنستخدم هذه المعلومات لتخصيص خطتك الشخصية",
    "exercise_frequency": "كم مرة تتمرّن بالأسبوع؟",
    "exercise_frequency_none": "ما بتمرّن",
    "exercise_frequency_low": "رياضي خفيف",
    "exercise_frequency_medium": "رياضي منتظم",
    "exercise_frequency_high": "رياضي محترف",
    "height": "الطول",
    "weight": "الوزن",
    "have_you_tried_another_app": "هل جربت تطبيقات أخرى لتتبع السعرات الحرارية؟",
    "yes": "نعم",
    "no": "لا",
    "orange_ai_achieves_long_term_results": "Orange AI يحقق نتائج طويلة المدى",
    "users_maintain_weight_loss_even_after_6_months": "80% من المستخدمين يحافظون على خسارة الوزن حتى بعد 6 أشهر",
    "with_orange_ai_youll_lose_more_weight_than_with_regular_methods": "مع OrangeAI ستتمكن من خسارة وزن أكثر من الطرق التقليدية",
    "orange": "OrangeAI",
    "with": "مع",
    "without": "بدون",
    "orange_make_it_easy": "Orange AI يجعل الأمور سهلة ويساعدك على الالتزام",
    "height_and_weight": "الطول والوزن",
    "birthday": "تاريخ ميلادك",
    "month": "الشهر",
    "day": "اليوم",
    "year": "السنة",
    "please_select_gender": "الرجاء اختيار الجنس",
    "please_select_exercise_frequency": "الرجاء تحديد وتيرة التمارين",
    "please_select_an_option": "الرجاء تحديد خيار",
    "please_confirm_height_and_weight": "الرجاء تأكيد الطول والوزن",
    "please_enter_valid_birth_date": "الرجاء إدخال تاريخ ميلاد صحيح",
    "target_weight_loss": "ما هو الوزن الذي ترغب الوصول إليه؟",
    "target_weight_gain": "ما هو الوزن الذي ترغب الوصول إليه؟",
    "target_weight_loss_description": "تريد إنقاص وزنك",
    "target_weight_gain_description": "تريد زيادة وزنك",
    "target_weight_value": "وزنك",
    "kg": "كجم",
    "current": "الحالي",
    "loss": "خسارة",
    "gain": "زيادة",
    "realistic_goal": "هدف واقعي",
    "onboarding_weight_loss_goal_subtitle": "ومعه لن يكون الأمر صعب عليك",
    "onboarding_weight_gain_goal_subtitle": "ومعه لن يكون الأمر صعب عليك",
    "onboarding_goal_statistics": "80% من المستخدمين يقولون أن التغيير واضح جدًا بعد استخدام Orange AI",
    "onboarding_kg": "كجم",
    "onboarding_target_weight_value": "الوزن المستهدف",
    "onboarding_current": "الحالي",
    "weekly_weight_loss_rate": "كم تريد خسارة أسبوعيًا؟",
    "weekly_weight_gain_rate": "كم تريد زيادة أسبوعيًا؟",
    "weight_loss_speed": "سرعة خسارة الوزن بالأسبوع",
    "weight_gain_speed": "سرعة زيادة الوزن بالأسبوع",
    "ideal_rate": "سرعة مثالية",
    "slow": "بطيء",
    "fast": "سريع",
    "days": "أيام",
    "dependeng_on_the_informaions": "بالاعتماد على معلومات OrangeAI",
    "Weight_loss_in_the_first_seven_days_is_slow_but_after_that_you_can_burn_fat_quickly": "نزول الوزن في أول 7 أيام يكون بطيء، لكن بعد ذلك يمكنك حرق الدهون بسرعة.",
    "youre_more_likely_to_achieve_your_goal": "لديك احتمالية كبيرة لتحقيق هدفك",
    "target_calories": "السعرات الحرارية المستهدفة",
    "target_carbs": "الكمية المستهدفة من الكربوهيدرات",
    "target_fats": "الكمية المستهدفة من الدهون",
    "target_proteins": "الكمية المستهدفة من البروتين",
    "manual_input": "إدخال يدوي",
    "instagram": "انستغرام",
    "facebook": "فيسبوك",
    "tiktok": "تيك توك",
    "youtube": "يوتيوب",
    "google": "جوجل",
    "else": "اخرى",
    "welcome": "أهلًا وسهلًا!",
    "welcome_desc": "أهلاً بك في عائلة",
    "welcome_second_desc": "نحن متحمسون لمساعدتك على تحسين لياقتك وحيويتك.",
    "target": "هدف إنقاص الوزن",
    "where_did_you_hear_of_us": "من وين سمعت عنّا؟",
    "set_up_everything_for_you": "نحن نجهز كل شيء لك.",
    "finishing_results": "جارٍ الانتهاء من النتائج...",
    "daily_recommendations_for": "التوصيات اليومية لـ",
    "health_result": "النتيجة الصحية",
    "calories": "السعرات الحرارية",
    "carbs": "الكربوهيدرات",
    "protien": "البروتين",
    "fat": "الدهون",
    "Your_goal_is_to_lose_weight": "هدفك هو إنقاص الوزن:",
    "Your_goal_is_to_maintain": "هدفك هو المحافظة على الوزن:",
    "Your_goal_is_to_gain_weight": "هدفك هو زيادة الوزن:",
    "kg_by": "كجم بحلول",
    "daily_recommendations": "التوصيات اليومية",
    "what_doesnot_make_you_commit": "ما الذي يمنعك من الالتزام؟",
    "lack_of_commitment": "قلة التزام",
    "unhealthy_eating_habits": "عادات أكل غير صحية",
    "lack_of_support": "قلة دعم",
    "busy_table": "جدول مشغول",
    "Lack_of_meal_ideas": "قلة أفكار للوجبات",
    "you_can_edit_anytime": "يمكنك التعديل في أي وقت",
    "what_is_your_diet": "ما هو نظامك الغذائي؟",
    "eat_everything": "آكل كل شيء",
    "keto": "كيتو",
    "pescetarian": "سمكي",
    "vegetarian": "نباتي",
    "vegan": "نباتي صرف",
    "meal_one": "الوجبة الأولى",
    "meal_two": "الوجبة الثانية",
    "meal_three": "الوجبة الثالثة",
    "waht_you_want_to_achieve": "ما الذي تود تحقيقه؟",
    "eat_healthy_and_live_well": "أتناول طعامًا صحيًا وأعيش حياة أفضل",
    "improve_energy_and_mood": "تحسين مستوى الطاقة والمزاج",
    "stay_active_and_consistent": "البقاء نشيطًا ومداومًا",
    "feel_satisfied_with_body": "أشعر بالرضا عن جسمي",
    "do_you_eat_all_meals_same_time": "هل تأكل وجباتك في نفس الوقت كل يوم عادة؟",
    "choose_meal_time": "اختر وقت الوجبة",
    "submit": "تأكيد",
    "cancel": "إلغاء",
    "share_your_opinion": "شاركنا رأيك",
    "How_has_your_experience_with_us_been_so_far": "كيف كانت تجربتك معنا حتى الآن؟ تقييمك يساعدنا على التطوير دائمًا.",
    "Reach_your_goal_and_activate_notifications": "اكمل رحلتك وفعّل الإشعارات",
    "You_can_turn_off_any_of_the_notifications_at_any_time": "يمكنك إيقاف أي من الإشعارات في أي وقت من الإعدادات. لن نرسل إليك رسائل مزعجة.",
    "Start_your_3day_free_trial": "ابدأ تجربتك المجانية لمدة 3 أيام للمتابعة.",
    "today": "اليوم",
    "yesterday": "البارحة",
    "Unlock_all_the_apps_features": "افتح جميع ميزات التطبيق مثل حساب السعرات بتقنية الذكاء الاصطناعي والمزيد.",
    "after_two_days_reminder": "بعد يومين – تذكير",
    "Well_send_you_a_reminder": "سنرسل لك تذكيرًا بأن التجربة المجانية ستنتهي قريبًا.",
    "after_3_days_Start_Subscription": "بعد 3 أيام – بدء الاشتراك",
    "You_will_be_charged_on_May_28_2025": "سيتم خصم الرسوم في 28 مايو 2025 ما لم تقم بإلغاء الاشتراك قبل ذلك.",
    "do_you_have_referral_code": "هل لديك كود خصم؟",
    "your_weight_changes": "تحولات وزنك",
    "you_can_skip_this_level": "يمكنك تخطي هذه المرحلة",
    "referral_code": "كود الخصم",
    "left_cals": "سعرات متبقية",
    "Add_up_to_200_calories_from_yesterday_to_todays_daily_goal": "أضف ما يصل إلى 200 سعرة حرارية من الأمس إلى هدف اليوم اليومي؟",
    "add_burned_calories_again": "أضف السعرات الحرارية المحروقة مرة أخرى للهدف اليومي؟",
    "daily_target": "الهدف اليومي",
    "run": "ركض",
    "cal": "سعرة",
    "calorie": "سعرة حرارية",
    "connect_to": "الاتصال بـ",
    "skip": "تخطي",
    "sync_your_daily_activity_between": "زامن نشاطك اليومي بين",
    "and_Apple_Health_for_more_comprehensive_data": "وApple Health للحصول على بيانات شاملة."
  },
  "home": {
    "settings": "الإعدادات",
    "progress": "التقدم",
    "home": "الرئيسية",
    "add": "إضافة",
    "remaining_carbs": "الكربوهيدرات",
    "remaining_proteins": "البروتين",
    "remaining_fats": "الدهون",
    "remaining_cals": "السعرات المتبقية",
    "burned_cals": "السعرات المحروقة",
    "burned_calories": "محروقة",
    "recently_added": "المضاف مؤخرًا",
    "gram": "غ",
    "delete": "حذف",
    "unknown": "غير معروف",
    "analyzing": "جارٍ التحليل...",
    "calculating_nutrition": "جارٍ حساب القيم الغذائية...",
    "processing_image": "جارٍ معالجة الصورة...",
    "identifying_food": "جارٍ تحديد الطعام...",
    "computing_calories": "جارٍ حساب السعرات الحرارية...",
    "automatically_calculated_nutrition": "القيم الغذائية محسوبة تلقائياً",
    "carbs_50_protein_20_fats_30": "كربوهيدرات 50% • بروتين 20% • دهون 30%",
    "calorie": "سعرة حرارية",
    "calories": "السعرات الحرارية",
    "protein": "البروتين",
    "carbs": "الكربوهيدرات",
    "fats": "الدهون",
    "grams": "جم",
    "save_changes": "حفظ التغييرات",
    "save": "حفظ",
    "edit_food_name": "تعديل اسم الطعام",
    "current_name": "الاسم الحالي",
    "new_name": "الاسم الجديد",
    "no_name": "بدون اسم",
    "enter_food_name": "أدخل اسم الطعام",
    "high_intensity_workout": "عالي الشدة",
    "low_intensity_workout": "منخفض الشدة",
    "moderate_intensity_workout": "متوسط الشدة",
    "add_ingredients": "إضافة المكونات",
    "ingredients": "المكونات",
    "edit_ingredient_amount": "تعديل كمية المكون",
    "nutrition_preview": "معاينة القيم الغذائية",
    "streak_days_0": "لسّا ما بلشت، يلا نبدأ!",
    "streak_days_1": "يوم واحد من الحماسة",
    "streak_days_2": "يومان من الحماسة",
    "streak_days_3_10": "أيام من الحماسة",
    "streak_days_10_plus": "يومًا من الحماسة",
    "weekdays_shortcuts": {
      "saturday": "س",
      "sunday": "أحد",
      "monday": "أث",
      "tuesday": "ث",
      "wednesday": "أر",
      "thursday": "خم",
      "friday": "جم"
    },
    "food_not_found_in_db": "لم يتم العثور على هذا العنصر في قاعدة البيانات"
  },
  "food_database": {
    "food_database": "قاعدة بيانات الطعام",
    "no_food": "لا يوجد أطعمة",
    "describe_what_you_ate": "صف ما أكلته",
    "all": "الكل",
    "my_meals": "وجباتي",
    "my_food": "أطعِمتي",
    "favorite_meals": "أطعمة محفوظة",
    "enter_your_meals": "أدخل طعامك",
    "entrance_recently": "المضاف مؤخرًا",
    "create_meal": "أنشئ وجبة",
    "enter_name": "أدخل الاسم",
    "meal_nutarians": "عناصر الوجبة",
    "add_nutrians_to_this_meal": "أضف عناصر لهذه الوجبة",
    "save": "حفظ",
    "enter_meal_name": "ادخل اسم الوجبة"
  },
  "progress": {
    "title": "التقدم",
    "this_week": "الأسبوع الحالي",
    "last_week": "الأسبوع السابق",
    "two_weeks_ago": "منذ أسبوعين",
    "three_weeks_ago": "منذ 3 أسابيع",
    "target_progress": "تقدم الهدف",
    "carbs": "كربوهيدرات",
    "fats": "دهون",
    "protein": "بروتين",
    "bmi": "BMI",
    "underweight": "نقص في الوزن",
    "healthy": "صحي",
    "overweight": "زيادة في الوزن",
    "obese": "سمنة",
    "scale_time": "وقت الميزان",
    "days": "أيام",
    "enter_weight": "أدخل الوزن",
    "registration_days": "أيام التسجيل",
    "months": "أشهر",
    "year": "سنة",
    "all": "الكل",
    "your_current_weight_is": "تصنيف وزنك الحالي هو:",
    "cals_sum": "مجموع السعرات الحرارية",
    "current_weight": "الوزن الحالي",
    "edit_weight": "تعديل الوزن",
    "save_changes": "حفظ التغييرات"
  },
  "settings": {
    "settings": "الإعدادات",
    "personal_details": "التفاصيل الشخصية",
    "customize_food_items": "ضبط العناصر الغذائية",
    "nutrition_and_weight": "الأهداف والوزن الحالي",
    "weight_log": "سجل الوزن",
    "new": "جديد",
    "language": "اللغة",
    "Preferences": "التفضيلات",
    "Add_the_calories_burned": "أضف السعرات الحرارية المحروقة",
    "Add_the_calories_burned_back_to_the_daily_goal": "أضف السعرات الحرارية المحروقة مرة أخرى إلى الهدف اليومي",
    "about_calories": "حول السعرات الحرارية",
    "Add_up_to_200_calories_from_yesterday_to_todays_daily_goal": "أضف ما يصل إلى 200 سعرة حرارية من الأمس إلى هدف اليوم اليومي",
    "Disable_nutrien_recalculation": "تعطيل إعادة حساب المغذيات",
    "Enable_this_option_to_manually_specify_calories_and_nutrients_when_editing_food": "فعّل هذا الخيار لتحديد السعرات والمغذيات يدويًا عند تعديل الطعام",
    "Terms_and_conditions": "الشروط والأحكام",
    "Privacy_Policy": "سياسة الخصوصية",
    "delete_account": "حذف الحساب",
    "log_out": "تسجيل الخروج"
  },
  "quick_actions": {
    "scan_food": "مسح الطعام",
    "add_exercise": "سجل تمرين",
    "food_database": "قاعدة بيانات الطعام",
    "saved_food": "أطعمة محفوظة",
    "select_language": "اختر اللغة"
  },
  "navbar": {
    "home": "الرئيسية",
    "settings": "الإعدادات",
    "progress": "التقدم",
    "add": "إضافة"
  },
  "auth": {
    "complete_account": "أكمل إعداد حسابك",
    "login_to_save_ur_data": "سجّل الدخول لحفظ بياناتك الغذائية وتتبع السعرات، الوجبات، ومزامنتها على جميع أجهزتك.",
    "login_with_google": "الدخول باستخدام Google",
    "login_with_apple": "الدخول باستخدام Apple ID"
  },
  "scan": {
    "gallery": "المعرض",
    "scan_food": "مسح الطعام",
    "retry": "حاول مجددًا",
    "barcode": "باركود",
    "food_label": "المعلومات الغذائية"
  },
  "payment": {
    "restore": "استعادة",
    "free_trial_start_message": "ابدأ تجربتك المجانية لمدة 3 أيام للمتابعة.",
    "no_payment_due": "لا يوجد دفع مستحق الآن",
    "free_for_3_days": "3 أيام مجانية",
    "choose_subscription_plan": "يرجى اختيار خطة الاشتراك",
    "start_3_days_free": "ابدأ التجربة المجانية لمدة 3 أيام",
    "try_for_free": "جرّب مجانًا!",
    "yearly_price_info": "فقط {price} في السنة ({monthlyPrice} شهريًا)",
    "one_time_offer": "عرض لمرة واحدة!",
    "never_see_again": "لن ترى هذا العرض مرة أخرى",
    "discount_message": "خصم 80% لك فقط",
    "promo_price": "فقط {price} شهريًا",
    "lowest_price_ever": "أقل سعر على الإطلاق",
    "claim_offer": "احصل على العرض المحدود الآن!"
  },
  "exercise": {
    "add_exercise": "أضف تمرين",
    "exercises": "التمارين",
    "run": {
      "title": "الجري",
      "subtitle": "الجري، الركض، العدو، وغيرها"
    },
    "weight_lifting": {
      "title": "رفع الأثقال",
      "subtitle": "أجهزة تمارين، أوزان حرة، وغيرها"
    },
    "describe_exercise": {
      "title": "وصف التمرين",
      "subtitle": "اكتب تمرينك بالنص"
    },
    "manual_entry": {
      "title": "إدخال يدوي",
      "subtitle": "أدخل عدد السعرات التي حرفتها يدويا"
    },
    "describe_exercise_hint": "صف وقت التمرين، وشدته، وما إلى ذلك.",
    "example": "'مثال: يوغا لـ60 دقيقة، مع التمدد والاسترخاء'",
    "target_calories": "السعرات الحرارية المستهدفة",
    "adjust_intensity": "ضبط الشدة",
    "run_high": {
      "title": "عالي",
      "subtitle": "الركض السريع - 14 ميلا بالساعة (4 دقائق لكل ميل)",
      "intensityLevel": 2
    },
    "run_medium": {
      "title": "متوسط",
      "subtitle": "الركض - 6 اميال بالساعة (10 دقائق لكل ميل)",
      "intensityLevel": 1
    },
    "run_low": {
      "title": "منخفض",
      "subtitle": "المشي الهادئ - 3 اميال بالساعة (20 دقيقة لكل ميل)",
      "intensityLevel": 0
    },
    "weight_lifting_high": {
      "title": "عالي",
      "subtitle": "رفع أثقال ثقيل – شدة عالية (تكرارات قليلة، وزن ثقيل)",
      "intensityLevel": 2
    },
    "weight_lifting_medium": {
      "title": "متوسط",
      "subtitle": "رفع أثقال متوسط – شدة متوسطة (توازن بين التكرارات والوزن)",
      "intensityLevel": 1
    },
    "weight_lifting_low": {
      "title": "منخفض",
      "subtitle": "رفع أثقال خفيف – شدة منخفضة (تكرارات كثيرة، وزن خفيف)",
      "intensityLevel": 0
    },
    "duration": "المدة",
    "duration_in_mins": "المدلة بالدقائق",
    "enter_duration": "أدخل المدة",
    "next": "التالي"
  },
  "streak_days": {
    "zero": "لم تبدأ السلسلة بعد!",
    "one": "سلسلة ليوم واحد",
    "two": "سلسلة ليومين",
    "few": "سلسلة لـ {count} أيام",
    "many": "سلسلة لـ {count} يومًا",
    "other": "سلسلة لـ {count} أيام"
  }
};
static const Map<String,dynamic> _en = {
  "common": {
    "ma": "Male",
    "fe": "Female",
    "months": {
      "january": "January",
      "february": "February",
      "march": "March",
      "april": "April",
      "may": "May",
      "june": "June",
      "july": "July",
      "august": "August",
      "september": "September",
      "october": "October",
      "november": "November",
      "december": "December"
    },
    "select_language": "Select Language",
    "navbar": {
      "home": "Home",
      "settings": "Settings",
      "profile": "Profile",
      "add": "Add"
    },
    "calories": "Calories",
    "carbs": "Carbs",
    "protien": "Protein",
    "fat": "Fats",
    "servings": "Serving",
    "halal": "Halal",
    "haram": "Not-Halal",
    "health_score": "Health Score",
    "add": "Add",
    "no_internet_connection": "No Internet Connection",
    "back": "Back",
    "hour": "Hours",
    "minute": "Minutes",
    "do_u_want_to_logout": "Do you want to log out?",
    "confirm": "Confirm",
    "cancel": "Cancel",
    "continue": "Continue",
    "error_recognizing_food": "Error recognizing food, please try again",
    "next": "Next"
  },
  "onboarding": {
    "your_weight": "Your Weight",
    "lets_start": "Let's start",
    "gram": "g",
    "what_is_your_goal": "What's your goal?",
    "goal_weight_loss": "Weight Loss",
    "goal_maintenance": "Maintenance",
    "your_weight_changes": "Your Weight Changes",
    "goal_weight_gain": "Weight Gain",
    "one_photo": "One photo... and everything is calculated!",
    "one_photo_desc": "Snap your meal and let AI analyze it to calculate calories and nutrients accurately.",
    "halal": "Know what you eat... and if it’s Halal!",
    "halal_desc": "The app automatically distinguishes Halal from non-Halal foods and keeps you reassured anywhere.",
    "health": "Your health matters... and your goal is easier",
    "health_desc": "Track your calories, organize meals, and get a smart and simple nutrition plan.",
    "start": "Start",
    "next": "Next",
    "choose_your_gender": "Select your gender",
    "we_will_use_this_information_to_personalize_your_plan": "We will use this information to personalize your plan",
    "with_orange_ai_youll_lose_more_weight_than_with_regular_methods": "With Orange AI, you'll lose more weight than with regular methods",
    "exercise_frequency_none": "I don't exercise",
    "exercise_frequency": "How often do you exercise per week?",
    "exercise_frequency_low": "Light",
    "exercise_frequency_medium": "Regular",
    "exercise_frequency_high": "Athlete",
    "height": "Height",
    "weight": "Weight",
    "have_you_tried_another_app": "Have you tried other calorie tracking apps?",
    "yes": "Yes",
    "no": "No",
    "orange_ai_achieves_long_term_results": "Orange AI delivers long-term results",
    "users_maintain_weight_loss_even_after_6_months": "80% of users maintain weight loss even after 6 months",
    "orange": "OrangeAI",
    "with": "With",
    "without": "Without",
    "orange_make_it_easy": "OrangeAI makes your goal easier!",
    "height_and_weight": "Height and Weight",
    "birthday": "Date of Birth",
    "month": "Month",
    "day": "Day",
    "year": "Year",
    "please_select_gender": "Please select gender",
    "please_select_exercise_frequency": "Please select exercise frequency",
    "please_select_an_option": "Please select an option",
    "please_confirm_height_and_weight": "Please confirm height and weight",
    "please_enter_valid_birth_date": "Please enter a valid birth date",
    "target_weight_loss": "What weight do you want to reach?",
    "target_weight_gain": "What weight do you want to reach?",
    "target_weight_loss_description": "Set your target weight to help us create your personalized weight loss plan",
    "target_weight_gain_description": "Set your target weight to help us create your personalized weight gain plan",
    "target_weight_value": "Your target weight",
    "kg": "KG",
    "current": "Current",
    "loss": "Losing",
    "gain": "Gaining",
    "instagram": "Instagram",
    "facebook": "Facebook",
    "tiktok": "TikTok",
    "youtube": "YouTube",
    "google": "Google",
    "else": "Else",
    "realistic_goal": "is a realistic goal",
    "onboarding_weight_loss_goal_subtitle": "and you can achieve it",
    "onboarding_weight_gain_goal_subtitle": "and you can achieve it",
    "onboarding_goal_statistics": "80% of users report that change became easier and clearer after using Orange AI",
    "onboarding_kg": "kg",
    "onboarding_target_weight_value": "Target Weight",
    "onboarding_current": "Current",
    "weekly_weight_loss_rate": "How much do you want to lose per week?",
    "weekly_weight_gain_rate": "How much do you want to gain per week?",
    "weight_loss_speed": "Weekly weight loss rate",
    "weight_gain_speed": "Weekly weight gain rate",
    "ideal_rate": "Ideal rate",
    "welcome": "Welcome",
    "welcome_desc": "Welcome to the family of ",
    "welcome_second_desc": "We are excited to have you on board!",
    "target": "Weight Target",
    "where_did_you_hear_of_us": "Where did you hear of us?",
    "set_up_everything_for_you": "Set up everything for you",
    "finishing_results": "Finishing results",
    "daily_recommendations_for": "Daily recommendations for",
    "calories": "Calories",
    "carbs": "Carbs",
    "protien": "Protein",
    "fat": "Fat",
    "health_result": "Health result",
    "Your_goal_is_to_lose_weight": "Your goal is to lose weight",
    "Your_goal_is_to_maintain": "Your goal is to maintain",
    "Your_goal_is_to_gain_weight": "Your goal is to gain weight",
    "kg_by": "kg by",
    "daily_recommendations": "Daily Recommendations",
    "what_doesnot_make_you_commit": "What is keeping you from committing?",
    "lack_of_commitment": "Lack of commitment",
    "unhealthy_eating_habits": "Unhealthy eating habits",
    "lack_of_support": "Lack of support",
    "busy_table": "Busy schedule",
    "Lack_of_meal_ideas": "Lack of meal ideas",
    "you_can_edit_anytime": "You can edit anytime",
    "what_is_your_diet": "What's your diet?",
    "eat_everything": "I eat everything",
    "keto": "Keto",
    "pescetarian": "Pescetarian",
    "vegetarian": "Vegetarian",
    "vegan": "Vegan",
    "waht_you_want_to_achieve": "What do you want to achieve?",
    "eat_healthy_and_live_well": "Eat healthy and live well",
    "improve_energy_and_mood": "Improve my energy and mood",
    "stay_active_and_consistent": "Stay active and consistent",
    "feel_satisfied_with_body": "Feel satisfied with my body",
    "meal_one": "Meal One",
    "meal_two": "Meal Two",
    "meal_three": "Meal Three",
    "do_you_eat_all_meals_same_time": "Do you eat all meals at the same time?",
    "share_your_opinion": "Share your opinion",
    "How_has_your_experience_with_us_been_so_far": "How has your experience with us been so far?",
    "Reach_your_goal_and_activate_notifications": "Reach your goal and activate notifications",
    "You_can_turn_off_any_of_the_notifications_at_any_time": "You can turn off any of the notifications at any time",
    "Start_your_3day_free_trial": "Start your 3-day free trial to continue.",
    "today": "Today",
    "yesterday ": "Yesterday",
    "slow": "Slow",
    "fast": "Fast",
    "Unlock_all_the_apps_features": "Unlock all app features like calorie tracking, AI technology, and more.",
    "after_two_days_reminder": "After two days – Reminder",
    "Wellsend_you_a_reminder": "We'll send you a reminder that your free trial is ending soon.",
    "3_days_later_Start_Subscription": "After 3 days – Start Subscription",
    "You_will_be_charged_on_May_28_2025": "You will be charged on May 28, 2025 unless you cancel beforehand.",
    "do_you_have_referral_code": "Do you have a referral code?",
    "you_can_skip_this_level": "You can skip this step.",
    "referral_code": "Referral Code",
    "left_cals": "Left Cals",
    "days": "Days",
    "dependeng_on_the_informaions": "Depending on OrangeAI's information",
    "Weight_loss_in_the_first_seven_days_is_slow_but_after_that_you_can_burn_fat_quickly": "Weight loss in the first 7 days is slow, but after that you can burn fat quickly.",
    "youre_more_likely_to_achieve_your_goal": "You're more likely to achieve your goal",
    "Add_up_to_200_calories_from_yesterday_to_todays_daily_goal": "Add up to 200 calories from yesterday to today's daily goal?",
    "add_burned_calories_again": "Add burned calories again to the daily goal?",
    "daily_target": "Daily Target",
    "run": "Run",
    "manual_input": "Manual Input",
    "target_carbs": "Target Carbohydrates",
    "target_fats": "Target Fats",
    "target_proteins": "Target Protein",
    "target_calories": "Target Calories",
    "cal": "Cal",
    "calorie": "Calorie",
    "connect_to": "Connect to",
    "skip": "Skip",
    "sync_your_daily_activity_between": "Sync your daily activity between",
    "and_Apple_Health_for_more_comprehensive_data": "and Apple Health for more comprehensive data."
  },
  "home": {
    "settings": "Settings",
    "progress": "Progress",
    "home": "Home",
    "add": "Add",
    "remaining_carbs": "Carbs",
    "remaining_proteins": "Proteins",
    "remaining_fats": "Fats",
    "remaining_cals": "Calories Remaining",
    "burned_cals": "Burned Calories",
    "burned_calories": "Burned",
    "recently_added": "Recently Added",
    "gram": "g",
    "delete": "Delete",
    "unknown": "Unknown",
    "analyzing": "Analyzing...",
    "calculating_nutrition": "Calculating nutrition...",
    "processing_image": "Processing image...",
    "identifying_food": "Identifying food...",
    "computing_calories": "Computing calories...",
    "automatically_calculated_nutrition": "Automatically calculated nutrition",
    "carbs_50_protein_20_fats_30": "Carbs 50% • Protein 20% • Fats 30%",
    "calorie": "Calorie",
    "calories": "Calories",
    "protein": "Protein",
    "carbs": "Carbs",
    "fats": "Fats",
    "grams": "Grams",
    "save_changes": "Save Changes",
    "save": "Save",
    "edit_food_name": "Edit Food Name",
    "current_name": "Current Name",
    "new_name": "New Name",
    "no_name": "No Name",
    "enter_food_name": "Enter food name",
    "high_intensity_workout": "High Intensity",
    "low_intensity_workout": "Low Intensity",
    "moderate_intensity_workout": "Moderate Intensity",
    "add_ingredients": "Add Ingredients",
    "ingredients": "Ingredients",
    "edit_ingredient_amount": "Edit Ingredient Amount",
    "nutrition_preview": "Nutrition Preview",
    "streak_days_0": "You haven't started yet, let's start!",
    "streak_days_1": "One day of excitement",
    "streak_days_2": "Two days of excitement",
    "streak_days_3_10": "Days of excitement",
    "streak_days_10_plus": "Day of excitement",
    "weekdays_shortcuts": {
      "saturday": "S",
      "sunday": "Su",
      "monday": "M",
      "tuesday": "Tu",
      "wednesday": "W",
      "thursday": "Th",
      "friday": "F"
    },
    "food_not_found_in_db": "This item was not found in our database"
  },
  "food_database": {
    "food_database": "Food Database",
    "no_food": "No Food",
    "save": "Save",
    "describe_what_you_ate": "Describe what you ate",
    "all": "All",
    "my_meals": "My Meals",
    "my_food": "My Foods",
    "favorite_meals": "Saved Foods",
    "enter_your_meals": "Enter your meal",
    "entrance_recently": "Recently Added",
    "create_meal": "Create Meal",
    "enter_name": "Enter Name",
    "meal_nutarians": "Meal Nutrients",
    "add_nutrians_to_this_meal": "Add Nutrients to this Meal",
    "enter_meal_name": "Enter meal's name"
  },
  "progress": {
    "title": "Progress",
    "this_week": "This Week",
    "last_week": "Last Week",
    "two_weeks_ago": "2 Weeks Ago",
    "three_weeks_ago": "3 Weeks Ago",
    "target_progress": "Target Progress",
    "carbs": "Carbs",
    "fats": "Fats",
    "protein": "Protein",
    "bmi": "BMI",
    "underweight": "Underweight",
    "healthy": "Healthy",
    "overweight": "Overweight",
    "obese": "Obese",
    "scale_time": "Scale Time",
    "days": "Days",
    "enter_weight": "Enter Weight",
    "registration_days": "Registration Days",
    "months": "Months",
    "year": "Year",
    "all": "All",
    "your_current_weight_is": "Your current weight category is:",
    "cals_sum": "Sum Calories",
    "choose_meal_time": "Choose Meal Time",
    "submit": "Submit",
    "cancel": "Cancel",
    "current_weight": "Current Weight",
    "edit_weight": "Edit Weight",
    "save_changes": "Save Changes"
  },
  "settings": {
    "settings": "Settings",
    "personal_details": "Personal Details",
    "customize_food_items": "Customize Food Items",
    "nutrition_and_weight": "Goals and Current Weight",
    "weight_log": "Weight Log",
    "new": "New",
    "language": "Language",
    "Preferences": "Preferences",
    "Add_the_calories_burned": "Add Burned Calories",
    "Add_the_calories_burned_back_to_the_daily_goal": "Add burned calories back to the daily goal",
    "about_calories": "About Calories",
    "Add_up_to_200_calories_from_yesterday_to_todays_daily_goal": "Add up to 200 calories from yesterday to today’s goal",
    "Disable_nutrien_recalculation": "Disable Nutrient Recalculation",
    "Enable_this_option_to_manually_specify_calories_and_nutrients_when_editing_food": "Enable this to manually specify calories and nutrients when editing food",
    "Terms_and_conditions": "Terms and Conditions",
    "Privacy_Policy": "Privacy Policy",
    "delete_account": "Delete Account",
    "log_out": "Log Out"
  },
  "quick_actions": {
    "scan_food": "Scan Food",
    "add_exercise": "Log Exercise",
    "food_database": "Food Database",
    "saved_food": "Saved Foods"
  },
  "navbar": {
    "home": "Home",
    "settings": "Settings",
    "progress": "Progress",
    "add": "Add"
  },
  "auth": {
    "complete_account": "Complete your account setup",
    "login_to_save_ur_data": "Log in to save your nutrition data, track calories and meals, and sync across all your devices.",
    "login_with_google": "Log in with Google",
    "login_with_apple": "Log in with Apple ID",
    "saved_food": "Saved Foods",
    "select_language": "Select Language"
  },
  "scan": {
    "gallery": "Gallery",
    "scan_food": "Scan",
    "retry": "Retry",
    "barcode": "Barcode",
    "food_label": "Food Label"
  },
  "payment": {
    "restore": "Restore",
    "free_trial_start_message": "Start your 3-day free trial to continue.",
    "no_payment_due": "No payment due now",
    "free_for_3_days": "3 days free",
    "choose_subscription_plan": "Please choose a subscription plan",
    "start_3_days_free": "Start the 3-day free trial",
    "try_for_free": "Try for free!",
    "yearly_price_info": "Just {price} per year ({monthlyPrice}/mo)",
    "one_time_offer": "One time offer!",
    "never_see_again": "You will never see this again",
    "discount_message": "Here's a 80% off discount",
    "promo_price": "Only {price}/month",
    "lowest_price_ever": "Lowest price ever",
    "claim_offer": "Claim your limited offer now!"
  },
  "exercise": {
    "add_exercise": "Log Exercise",
    "exercises": "Exercises",
    "run": {
      "title": "Run",
      "subtitle": "Running, jogging, sprinting, etc."
    },
    "weight_lifting": {
      "title": "Weight Lifting",
      "subtitle": "Machines, free weights, etc."
    },
    "describe_exercise": {
      "title": "Describe Exercise",
      "subtitle": "Write your exercise in text"
    },
    "manual_entry": {
      "title": "Manual Entry",
      "subtitle": "Enter the calories manually"
    },
    "describe_exercise_hint": "Describe the exercise, its intensity, etc.",
    "example": "Example: 60 minutes of yoga, including stretching and relaxation",
    "target_calories": "Target Calories",
    "adjust_intensity": "Adjust Intensity",
    "run_high": {
      "title": "High",
      "subtitle": "Sprinting – 14 mph (4 minutes per mile)",
      "intensityLevel": 2
    },
    "run_medium": {
      "title": "Medium",
      "subtitle": "Running – 6 mph (10 minutes per mile)",
      "intensityLevel": 1
    },
    "rin_low": {
      "title": "Low",
      "subtitle": "Light walking – 3 mph (20 minutes per mile)",
      "intensityLevel": 0
    },
    "weight_lifting_high": {
      "title": "High",
      "subtitle": "Heavy weightlifting – High intensity (low reps, heavy load)",
      "intensityLevel": 2
    },
    "weight_lifting_medium": {
      "title": "Medium",
      "subtitle": "Moderate weightlifting – Moderate intensity (balanced reps & weight)",
      "intensityLevel": 1
    },
    "weight_lifting_low": {
      "title": "Low",
      "subtitle": "Light weightlifting – Low intensity (high reps, light weight)",
      "intensityLevel": 0
    },
    "duration": "Duration",
    "duration_in_mins": "Duration in minutes",
    "enter_duration": "Enter the duration",
    "next": "Next"
  },
  "streak_days": {
    "zero": "No streak yet! Get started today!",
    "one": "1 day streak",
    "two": "2 days streak",
    "few": "{count} days streak",
    "many": "{count} days streak",
    "other": "{count} days streak"
  }
};
static const Map<String, Map<String,dynamic>> mapLocales = {"ar": _ar, "en": _en};
}
