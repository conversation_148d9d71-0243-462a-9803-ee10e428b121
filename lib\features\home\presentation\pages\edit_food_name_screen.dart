import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EditFoodNameScreen extends StatefulWidget {
  final FoodModel foodModel;

  const EditFoodNameScreen({
    super.key,
    required this.foodModel,
  });

  @override
  State<EditFoodNameScreen> createState() => _EditFoodNameScreenState();
}

class _EditFoodNameScreenState extends State<EditFoodNameScreen> {
  late TextEditingController _nameController;
  late FocusNode _focusNode;
  bool _hasChanges = false;
  bool _isLoading = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    // Don't initialize the controller here - move to didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Only initialize once
    if (!_isInitialized) {
      // Initialize with current food name based on locale
      final currentName = _getCurrentFoodName();
      _nameController = TextEditingController(text: currentName);
      _nameController.addListener(_onNameChanged);

      // Auto-focus the text field
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });

      _isInitialized = true;
    }
  }

  String _getCurrentFoodName() {
    if (context.locale == const Locale('ar')) {
      return widget.foodModel.arabicName ?? widget.foodModel.dish ?? '';
    } else {
      return widget.foodModel.englishName ?? widget.foodModel.dish ?? '';
    }
  }

  void _onNameChanged() {
    final newName = _nameController.text.trim();
    final currentName = _getCurrentFoodName();

    setState(() {
      _hasChanges = newName != currentName && newName.isNotEmpty;
    });
  }

  Future<void> _saveChanges() async {
    if (!_hasChanges || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newName = _nameController.text.trim();

      // Update the appropriate name field based on locale
      FoodModel updatedFoodModel;
        updatedFoodModel = widget.foodModel.copyWith(
          arabicName: newName,
          englishName: newName,
        );


      // Use UpdateFoodWithDifferences with zero nutrition differences since we're only changing the name
      context.read<RecentActivityBloc>().add(UpdateFoodWithDifferences(
            meal: updatedFoodModel,
            caloriesDiff: 0.0,
            carbsDiff: 0.0,
            proteinDiff: 0.0,
            fatDiff: 0.0,
          ));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }

    context.pop();
  }

  @override
  void dispose() {
    if (_isInitialized) {
      _nameController.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: context.background,
      appBar: AppBar(
        backgroundColor: context.background,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(
            Icons.arrow_back,
            color: context.primaryColor,
          ),
        ),
        title: Text(
          LocaleKeys.home_edit_food_name.tr(),
          style: context.textTheme.titleLarge!.copyWith(
            color: context.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current food info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: context.onPrimaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(25),
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.restaurant,
                      color: context.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.home_current_name.tr(),
                            style: context.textTheme.bodySmall!.copyWith(
                              color: context.onSecondary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getCurrentFoodName().isEmpty ? LocaleKeys.home_no_name.tr() : _getCurrentFoodName(),
                            style: context.textTheme.titleMedium!.copyWith(
                              color: context.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Text field for editing name
              Text(
                LocaleKeys.home_new_name.tr(),
                style: context.textTheme.titleMedium!.copyWith(
                  color: context.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 8),

              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: context.onPrimaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(25),
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: _nameController,
                  focusNode: _focusNode,
                  style: context.textTheme.titleMedium!.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.start,
                  maxLines: 1,
                  textInputAction: TextInputAction.done,
                  onFieldSubmitted: (_) => _saveChanges(),
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(
                        color: context.primaryColor,
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 16,
                    ),
                    hintText: LocaleKeys.home_enter_food_name.tr(),
                    hintStyle: context.textTheme.titleMedium!.copyWith(
                      color: context.onSecondary.withAlpha(100),
                    ),
                    suffixIcon: Icon(
                      Icons.edit,
                      color: context.onSecondary,
                    ),
                  ),
                ),
              ),

              const Spacer(),

              // Save button (full width)
              if (_hasChanges)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveChanges,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            LocaleKeys.home_save_changes.tr(),
                            style: context.textTheme.titleMedium!.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
