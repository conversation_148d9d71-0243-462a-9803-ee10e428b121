part of 'subscription_bloc.dart';

/// Base class for all subscription events
abstract class SubscriptionEvent extends Equatable {
  const SubscriptionEvent();

  @override
  List<Object?> get props => [];
}

/// Event to initialize the subscription service
class InitializeSubscription extends SubscriptionEvent {
  const InitializeSubscription({this.appUserId});

  final String? appUserId;

  @override
  List<Object?> get props => [appUserId];
}

/// Event to load available subscription packages
class LoadSubscriptionPackages extends SubscriptionEvent {
  const LoadSubscriptionPackages({this.offeringIdentifier});

  final String? offeringIdentifier;

  @override
  List<Object?> get props => [offeringIdentifier];
}

/// Event to purchase a subscription package
class PurchaseSubscription extends SubscriptionEvent {
  const PurchaseSubscription(this.packageIdentifier);

  final String packageIdentifier;

  @override
  List<Object> get props => [packageIdentifier];
}

/// Event to restore previous purchases
class RestoreSubscriptions extends SubscriptionEvent {
  const RestoreSubscriptions();
}

/// Event to reset the subscription state
class ResetSubscriptionState extends SubscriptionEvent {
  const ResetSubscriptionState();
}
