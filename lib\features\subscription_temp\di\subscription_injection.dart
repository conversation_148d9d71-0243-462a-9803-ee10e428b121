import 'package:get_it/get_it.dart';

import '../presentation/bloc/subscription_bloc.dart';
import '../services/revenue_cat_service.dart';

/// Simplified dependency injection setup for subscription_temp feature
class SubscriptionTempInjection {
  static void init() {
    final sl = GetIt.instance;

    // Services
    sl.registerLazySingleton<RevenueCatService>(() => RevenueCatService());

    // BLoC
    sl.registerFactory(() => SubscriptionBloc(sl()));
  }
}
