part of 'subscription_bloc.dart';

/// Enum representing the status of subscription operations
enum SubscriptionBlocStatus {
  /// Initial state before any operations
  initial,

  /// Loading state during operations
  loading,

  /// Packages have been successfully loaded
  packagesLoaded,

  /// Purchase is in progress
  purchasing,

  /// Purchase completed successfully
  purchaseSuccess,

  /// Purchases restored successfully
  restoreSuccess,

  /// An error occurred
  error,
}

/// Simplified state class for subscription management
class SubscriptionState extends Equatable {
  const SubscriptionState({
    this.status = SubscriptionBlocStatus.initial,
    this.packages = const [],
    this.customerInfo,
    this.errorMessage,
    this.isInitialized = false,
  });

  /// Current status of the subscription operations
  final SubscriptionBlocStatus status;

  /// List of available subscription packages
  final List<Package> packages;

  /// Current customer info from RevenueCat
  final CustomerInfo? customerInfo;

  /// Error message when status is error
  final String? errorMessage;

  /// Whether the subscription service is initialized
  final bool isInitialized;

  /// Create a copy of this state with updated values
  SubscriptionState copyWith({
    SubscriptionBlocStatus? status,
    List<Package>? packages,
    CustomerInfo? customerInfo,
    String? errorMessage,
    bool? isInitialized,
    bool clearErrorMessage = false,
  }) {
    return SubscriptionState(
      status: status ?? this.status,
      packages: packages ?? this.packages,
      customerInfo: customerInfo ?? this.customerInfo,
      errorMessage: clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  /// Convenience getters for checking state
  bool get isLoading => status == SubscriptionBlocStatus.loading;
  bool get hasError => status == SubscriptionBlocStatus.error;
  bool get hasPackages => status == SubscriptionBlocStatus.packagesLoaded && packages.isNotEmpty;
  bool get isPurchasing => status == SubscriptionBlocStatus.purchasing;
  bool get isPurchaseSuccess => status == SubscriptionBlocStatus.purchaseSuccess;
  bool get isRestoreSuccess => status == SubscriptionBlocStatus.restoreSuccess;

  /// Check if user has any active subscription
  bool get hasActiveSubscription => customerInfo?.entitlements.active.isNotEmpty ?? false;

  @override
  List<Object?> get props => [
        status,
        packages,
        customerInfo,
        errorMessage,
        isInitialized,
      ];
}
