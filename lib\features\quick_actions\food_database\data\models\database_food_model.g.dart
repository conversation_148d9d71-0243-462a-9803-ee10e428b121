// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database_food_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetDatabaseFoodModelCollection on Isar {
  IsarCollection<DatabaseFoodModel> get databaseFoodModels => this.collection();
}

const DatabaseFoodModelSchema = CollectionSchema(
  name: r'DatabaseFoodModel',
  id: 2666972659212452242,
  properties: {
    r'arName': PropertySchema(
      id: 0,
      name: r'arName',
      type: IsarType.string,
    ),
    r'calories': PropertySchema(
      id: 1,
      name: r'calories',
      type: IsarType.long,
    ),
    r'carbs': PropertySchema(
      id: 2,
      name: r'carbs',
      type: IsarType.double,
    ),
    r'date': PropertySchema(
      id: 3,
      name: r'date',
      type: IsarType.dateTime,
    ),
    r'enName': PropertySchema(
      id: 4,
      name: r'enName',
      type: IsarType.string,
    ),
    r'fat': PropertySchema(
      id: 5,
      name: r'fat',
      type: IsarType.double,
    ),
    r'foodUrl': PropertySchema(
      id: 6,
      name: r'foodUrl',
      type: IsarType.string,
    ),
    r'halal': PropertySchema(
      id: 7,
      name: r'halal',
      type: IsarType.bool,
    ),
    r'id': PropertySchema(
      id: 8,
      name: r'id',
      type: IsarType.string,
    ),
    r'imagePath': PropertySchema(
      id: 9,
      name: r'imagePath',
      type: IsarType.string,
    ),
    r'ingredients': PropertySchema(
      id: 10,
      name: r'ingredients',
      type: IsarType.stringList,
    ),
    r'isDatabase': PropertySchema(
      id: 11,
      name: r'isDatabase',
      type: IsarType.bool,
    ),
    r'isFavoriteMeal': PropertySchema(
      id: 12,
      name: r'isFavoriteMeal',
      type: IsarType.bool,
    ),
    r'isFromSearch': PropertySchema(
      id: 13,
      name: r'isFromSearch',
      type: IsarType.bool,
    ),
    r'isMealCreated': PropertySchema(
      id: 14,
      name: r'isMealCreated',
      type: IsarType.bool,
    ),
    r'protein': PropertySchema(
      id: 15,
      name: r'protein',
      type: IsarType.double,
    ),
    r'remoteLogId': PropertySchema(
      id: 16,
      name: r'remoteLogId',
      type: IsarType.long,
    ),
    r'serving': PropertySchema(
      id: 17,
      name: r'serving',
      type: IsarType.string,
    )
  },
  estimateSize: _databaseFoodModelEstimateSize,
  serialize: _databaseFoodModelSerialize,
  deserialize: _databaseFoodModelDeserialize,
  deserializeProp: _databaseFoodModelDeserializeProp,
  idName: r'localId',
  indexes: {
    r'date': IndexSchema(
      id: -7552997827385218417,
      name: r'date',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'date',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _databaseFoodModelGetId,
  getLinks: _databaseFoodModelGetLinks,
  attach: _databaseFoodModelAttach,
  version: '3.1.0+1',
);

int _databaseFoodModelEstimateSize(
  DatabaseFoodModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.arName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.enName;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.foodUrl;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.id;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.imagePath;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  bytesCount += 3 + object.ingredients.length * 3;
  {
    for (var i = 0; i < object.ingredients.length; i++) {
      final value = object.ingredients[i];
      bytesCount += value.length * 3;
    }
  }
  {
    final value = object.serving;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _databaseFoodModelSerialize(
  DatabaseFoodModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.arName);
  writer.writeLong(offsets[1], object.calories);
  writer.writeDouble(offsets[2], object.carbs);
  writer.writeDateTime(offsets[3], object.date);
  writer.writeString(offsets[4], object.enName);
  writer.writeDouble(offsets[5], object.fat);
  writer.writeString(offsets[6], object.foodUrl);
  writer.writeBool(offsets[7], object.halal);
  writer.writeString(offsets[8], object.id);
  writer.writeString(offsets[9], object.imagePath);
  writer.writeStringList(offsets[10], object.ingredients);
  writer.writeBool(offsets[11], object.isDatabase);
  writer.writeBool(offsets[12], object.isFavoriteMeal);
  writer.writeBool(offsets[13], object.isFromSearch);
  writer.writeBool(offsets[14], object.isMealCreated);
  writer.writeDouble(offsets[15], object.protein);
  writer.writeLong(offsets[16], object.remoteLogId);
  writer.writeString(offsets[17], object.serving);
}

DatabaseFoodModel _databaseFoodModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = DatabaseFoodModel(
    arName: reader.readStringOrNull(offsets[0]),
    calories: reader.readLongOrNull(offsets[1]),
    carbs: reader.readDoubleOrNull(offsets[2]),
    date: reader.readDateTimeOrNull(offsets[3]),
    enName: reader.readStringOrNull(offsets[4]),
    fat: reader.readDoubleOrNull(offsets[5]),
    foodUrl: reader.readStringOrNull(offsets[6]),
    halal: reader.readBoolOrNull(offsets[7]),
    id: reader.readStringOrNull(offsets[8]),
    imagePath: reader.readStringOrNull(offsets[9]),
    ingredients: reader.readStringList(offsets[10]) ?? const [],
    isDatabase: reader.readBoolOrNull(offsets[11]) ?? false,
    isFavoriteMeal: reader.readBoolOrNull(offsets[12]) ?? false,
    isFromSearch: reader.readBoolOrNull(offsets[13]) ?? false,
    isMealCreated: reader.readBoolOrNull(offsets[14]) ?? false,
    localId: id,
    protein: reader.readDoubleOrNull(offsets[15]),
    remoteLogId: reader.readLongOrNull(offsets[16]),
    serving: reader.readStringOrNull(offsets[17]),
  );
  return object;
}

P _databaseFoodModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readDoubleOrNull(offset)) as P;
    case 3:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readDoubleOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readBoolOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringList(offset) ?? const []) as P;
    case 11:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 12:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 13:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 14:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 15:
      return (reader.readDoubleOrNull(offset)) as P;
    case 16:
      return (reader.readLongOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _databaseFoodModelGetId(DatabaseFoodModel object) {
  return object.localId ?? Isar.autoIncrement;
}

List<IsarLinkBase<dynamic>> _databaseFoodModelGetLinks(
    DatabaseFoodModel object) {
  return [];
}

void _databaseFoodModelAttach(
    IsarCollection<dynamic> col, Id id, DatabaseFoodModel object) {
  object.localId = id;
}

extension DatabaseFoodModelQueryWhereSort
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QWhere> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhere> anyLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhere> anyDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'date'),
      );
    });
  }
}

extension DatabaseFoodModelQueryWhere
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QWhereClause> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      localIdEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: localId,
        upper: localId,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      localIdNotEqualTo(Id localId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: localId, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: localId, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      localIdGreaterThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: localId, includeLower: include),
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      localIdLessThan(Id localId, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: localId, includeUpper: include),
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      localIdBetween(
    Id lowerLocalId,
    Id upperLocalId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerLocalId,
        includeLower: includeLower,
        upper: upperLocalId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'date',
        value: [null],
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      dateEqualTo(DateTime? date) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'date',
        value: [date],
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      dateNotEqualTo(DateTime? date) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [],
              upper: [date],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [date],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [date],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [],
              upper: [date],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      dateGreaterThan(
    DateTime? date, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [date],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      dateLessThan(
    DateTime? date, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [],
        upper: [date],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterWhereClause>
      dateBetween(
    DateTime? lowerDate,
    DateTime? upperDate, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [lowerDate],
        includeLower: includeLower,
        upper: [upperDate],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension DatabaseFoodModelQueryFilter
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QFilterCondition> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'arName',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'arName',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'arName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'arName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'arName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'arName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'arName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'arName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'arName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'arName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'arName',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      arNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'arName',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calories',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calories',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calories',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calories',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calories',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      caloriesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calories',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'carbs',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'carbs',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'carbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'carbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'carbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      carbsBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'carbs',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      dateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'enName',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'enName',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'enName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'enName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'enName',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'enName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'enName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'enName',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'enName',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'enName',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      enNameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'enName',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fat',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fat',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      fatBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fat',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'foodUrl',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'foodUrl',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'foodUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'foodUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'foodUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'foodUrl',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'foodUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'foodUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'foodUrl',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'foodUrl',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'foodUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      foodUrlIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'foodUrl',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      halalIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'halal',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      halalIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'halal',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      halalEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'halal',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'id',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'id',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'id',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      idIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'id',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'imagePath',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'imagePath',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'imagePath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'imagePath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'imagePath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'imagePath',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      imagePathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'imagePath',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'ingredients',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'ingredients',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'ingredients',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'ingredients',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'ingredients',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      ingredientsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'ingredients',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isDatabaseEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isDatabase',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isFavoriteMealEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isFavoriteMeal',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isFromSearchEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isFromSearch',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      isMealCreatedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isMealCreated',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      localIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'localId',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      localIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'localId',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      localIdEqualTo(Id? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      localIdGreaterThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      localIdLessThan(
    Id? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localId',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      localIdBetween(
    Id? lower,
    Id? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'protein',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'protein',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'protein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'protein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'protein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      proteinBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'protein',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      remoteLogIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'remoteLogId',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      remoteLogIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'remoteLogId',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      remoteLogIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'remoteLogId',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      remoteLogIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'remoteLogId',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      remoteLogIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'remoteLogId',
        value: value,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      remoteLogIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'remoteLogId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'serving',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'serving',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'serving',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'serving',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'serving',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'serving',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'serving',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'serving',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'serving',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'serving',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'serving',
        value: '',
      ));
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterFilterCondition>
      servingIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'serving',
        value: '',
      ));
    });
  }
}

extension DatabaseFoodModelQueryObject
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QFilterCondition> {}

extension DatabaseFoodModelQueryLinks
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QFilterCondition> {}

extension DatabaseFoodModelQuerySortBy
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QSortBy> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByArName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arName', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByArNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arName', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByEnName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enName', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByEnNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enName', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy> sortByFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByFoodUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'foodUrl', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByFoodUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'foodUrl', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByHalal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'halal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByHalalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'halal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy> sortById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByImagePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByImagePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsDatabase() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsDatabaseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFavoriteMeal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFavoriteMealDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFromSearch() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsFromSearchDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsMealCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByIsMealCreatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByRemoteLogId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteLogId', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByRemoteLogIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteLogId', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByServing() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'serving', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      sortByServingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'serving', Sort.desc);
    });
  }
}

extension DatabaseFoodModelQuerySortThenBy
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QSortThenBy> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByArName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arName', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByArNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'arName', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calories', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'carbs', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByEnName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enName', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByEnNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'enName', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy> thenByFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fat', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByFoodUrl() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'foodUrl', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByFoodUrlDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'foodUrl', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByHalal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'halal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByHalalDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'halal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByImagePath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByImagePathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'imagePath', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsDatabase() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsDatabaseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isDatabase', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFavoriteMeal() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFavoriteMealDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFavoriteMeal', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFromSearch() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsFromSearchDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFromSearch', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsMealCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByIsMealCreatedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isMealCreated', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByLocalId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByLocalIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localId', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'protein', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByRemoteLogId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteLogId', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByRemoteLogIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'remoteLogId', Sort.desc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByServing() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'serving', Sort.asc);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QAfterSortBy>
      thenByServingDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'serving', Sort.desc);
    });
  }
}

extension DatabaseFoodModelQueryWhereDistinct
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct> {
  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByArName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'arName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calories');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'carbs');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByEnName({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'enName', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fat');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByFoodUrl({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'foodUrl', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByHalal() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'halal');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct> distinctById(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'id', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByImagePath({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'imagePath', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIngredients() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'ingredients');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsDatabase() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isDatabase');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsFavoriteMeal() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isFavoriteMeal');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsFromSearch() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isFromSearch');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByIsMealCreated() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isMealCreated');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'protein');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByRemoteLogId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'remoteLogId');
    });
  }

  QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QDistinct>
      distinctByServing({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'serving', caseSensitive: caseSensitive);
    });
  }
}

extension DatabaseFoodModelQueryProperty
    on QueryBuilder<DatabaseFoodModel, DatabaseFoodModel, QQueryProperty> {
  QueryBuilder<DatabaseFoodModel, int, QQueryOperations> localIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localId');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations> arNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'arName');
    });
  }

  QueryBuilder<DatabaseFoodModel, int?, QQueryOperations> caloriesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calories');
    });
  }

  QueryBuilder<DatabaseFoodModel, double?, QQueryOperations> carbsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'carbs');
    });
  }

  QueryBuilder<DatabaseFoodModel, DateTime?, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations> enNameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'enName');
    });
  }

  QueryBuilder<DatabaseFoodModel, double?, QQueryOperations> fatProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fat');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations> foodUrlProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'foodUrl');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool?, QQueryOperations> halalProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'halal');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations>
      imagePathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'imagePath');
    });
  }

  QueryBuilder<DatabaseFoodModel, List<String>, QQueryOperations>
      ingredientsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'ingredients');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations> isDatabaseProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isDatabase');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations>
      isFavoriteMealProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isFavoriteMeal');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations>
      isFromSearchProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isFromSearch');
    });
  }

  QueryBuilder<DatabaseFoodModel, bool, QQueryOperations>
      isMealCreatedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isMealCreated');
    });
  }

  QueryBuilder<DatabaseFoodModel, double?, QQueryOperations> proteinProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'protein');
    });
  }

  QueryBuilder<DatabaseFoodModel, int?, QQueryOperations>
      remoteLogIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'remoteLogId');
    });
  }

  QueryBuilder<DatabaseFoodModel, String?, QQueryOperations> servingProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'serving');
    });
  }
}
