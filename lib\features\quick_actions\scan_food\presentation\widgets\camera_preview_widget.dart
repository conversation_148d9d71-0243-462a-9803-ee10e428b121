// lib/features/quick_actions/scan_food/presentation/widgets/camera_preview_widget.dart

import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/features/quick_actions/scan_food/presentation/enums/scan_mode.dart';

class CameraPreviewWidget extends StatelessWidget {
  final CameraController? controller;
  final bool hasPermission;
  final String? errorMessage;
  final ScanMode currentMode;
  final VoidCallback? onRetry;

  const CameraPreviewWidget({
    super.key,
    required this.controller,
    required this.hasPermission,
    required this.errorMessage,
    required this.currentMode,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (!hasPermission) {
      return Container(
        color: Colors.black,
        child: Center(
          child: CircularProgressIndicator(color: context.primaryColor),
        ),
      );
    }

    if (errorMessage != null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                errorMessage!,
                style: const TextStyle(color: Colors.white, fontSize: 18),
                textAlign: TextAlign.center,
              ),
              if (onRetry != null) ...[
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: onRetry,
                  child: const Text('Retry'),
                ),
              ],
            ],
          ),
        ),
      );
    }

    if (currentMode == ScanMode.gallery) {
      return Container(
        color: const Color.fromARGB(117, 0, 0, 0),
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [],
          ),
        ),
      );
    }

    if (controller == null || !controller!.value.isInitialized) {
      return Container(
        color: Colors.black,
        child: Center(
          child: CircularProgressIndicator(color: context.primaryColor),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
        final cameraAspectRatio = controller!.value.aspectRatio;
        final previewAspectRatio = isPortrait ? 1 / cameraAspectRatio : cameraAspectRatio;

        final screenAspectRatio = constraints.maxWidth / constraints.maxHeight;
        final scale = previewAspectRatio / screenAspectRatio;

        return Transform.scale(
          scale: scale,
          child: Center(
            child: AspectRatio(
              aspectRatio: previewAspectRatio,
              child: CameraPreview(controller!),
            ),
          ),
        );
      },
    );
  }
}