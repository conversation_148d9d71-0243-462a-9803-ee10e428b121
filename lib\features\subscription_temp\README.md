# Simplified Subscription Feature

This feature handles core subscription management using RevenueCat SDK with a simplified architecture.

## Architecture

The feature uses a simplified architecture focusing on core functionality:

### Services
- **RevenueCatService**: Direct integration with RevenueCat SDK
  - Initialize RevenueCat
  - Get available packages
  - Purchase packages
  - Restore purchases
  - Check subscription status

### Presentation Layer
- **BLoC**: Simplified state management
  - `SubscriptionBloc`: Manages subscription state and operations
  - `SubscriptionEvent`: Core events (Initialize, LoadPackages, Purchase, Restore, Reset)
  - `SubscriptionState`: Simple state with packages, customer info, and status

- **Pages**: UI screens
  - `SubscriptionPlansScreen`: Main subscription plans screen
  - `SubscriptionPlansWrapper`: BLoC provider wrapper

- **Widgets**: Reusable UI components
  - `SubscriptionPackageCard`: Package display card

## Core Functionality

### ✅ Show Available Subscriptions
- Load packages from RevenueCat offerings
- Display packages in a user-friendly UI
- Show pricing, features, and popular badges

### ✅ Purchase Subscriptions
- Handle package purchases through RevenueCat
- Show loading states during purchase
- Handle purchase cancellations gracefully
- Update UI on successful purchase

### ✅ Restore Subscriptions
- Restore previous purchases
- Update subscription status
- Handle restore errors

### ✅ List Products in UI
- Clean, modern subscription cards
- Popular package highlighting
- Pricing and billing period display
- Intro pricing support

## Usage

### Initialize the feature
```dart
// In your main app initialization
SubscriptionTempInjection.init();
```

### Navigate to subscription screen
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const SubscriptionPlansWrapper(),
  ),
);
```

### Check subscription status
```dart
final bloc = context.read<SubscriptionBloc>();

// Check if user has active subscription
if (bloc.state.hasActiveSubscription) {
  // User has active subscription
}
```

## Configuration

### RevenueCat Setup
1. Update API keys in `RevenueCatService`:
   ```dart
   static const String _appleApiKey = 'YOUR_APPLE_API_KEY_HERE';
   static const String _googleApiKey = 'YOUR_GOOGLE_API_KEY_HERE';
   ```

2. Configure products in RevenueCat dashboard
3. Set up entitlements and offerings

## Dependencies

- `purchases_flutter`: RevenueCat SDK
- `flutter_bloc`: State management
- `injectable`: Dependency injection
- `get_it`: Service locator
