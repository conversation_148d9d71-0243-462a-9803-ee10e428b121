import 'dart:developer';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/bloc/recent_activity_bloc.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EditIngredientAmountScreen extends StatefulWidget {
  const EditIngredientAmountScreen({
    super.key,
    required this.foodModel,
    required this.ingredient,
    required this.ingredientIndex,
  });

  final FoodModel foodModel;
  final Ingredient ingredient;
  final int ingredientIndex;

  @override
  State<EditIngredientAmountScreen> createState() => _EditIngredientAmountScreenState();
}

class _EditIngredientAmountScreenState extends State<EditIngredientAmountScreen> {
  late TextEditingController _amountController;
  late FocusNode _focusNode;
  bool _hasChanges = false;
  bool _isLoading = false;

  // Store original nutrition values per unit (calculated from total amount)
  late double _perUnitCalories;
  late double _perUnitProtein;
  late double _perUnitCarbs;
  late double _perUnitFat;

  // Calculated nutrition values based on new amount
  int _calculatedCalories = 0;
  double _calculatedProtein = 0.0;
  double _calculatedCarbs = 0.0;
  double _calculatedFat = 0.0;

  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController(text: (widget.ingredient.amount ?? 1).toString());
    _focusNode = FocusNode();

    // Calculate per-unit nutrition values from total amount
    final currentAmount = widget.ingredient.amount ?? 1;
    _perUnitCalories = (widget.ingredient.calories ?? 0) / currentAmount;
    _perUnitProtein = (widget.ingredient.protein ?? 0.0) / currentAmount;
    _perUnitCarbs = (widget.ingredient.carbs ?? 0.0) / currentAmount;
    _perUnitFat = (widget.ingredient.fat ?? 0.0) / currentAmount;

    // Initialize calculated values with current amount
    _calculateNutritionValues(widget.ingredient.amount?.toDouble() ?? 1.0);

    _amountController.addListener(_onValueChanged);

    // Auto-focus and select all text
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
      _amountController.selection = TextSelection(
        baseOffset: 0,
        extentOffset: _amountController.text.length,
      );
    });
  }

  void _onValueChanged() {
    final newAmount = double.tryParse(_amountController.text) ?? 1.0;
    final currentAmount = widget.ingredient.amount?.toDouble() ?? 1.0;

    // Calculate nutrition values in real-time
    _calculateNutritionValues(newAmount);

    setState(() {
      _hasChanges = newAmount != currentAmount;
    });
  }

  /// Calculate nutrition values based on the new amount
  void _calculateNutritionValues(double amount) {
    _calculatedCalories = (_perUnitCalories * amount).round();
    _calculatedProtein = _perUnitProtein * amount;
    _calculatedCarbs = _perUnitCarbs * amount;
    _calculatedFat = _perUnitFat * amount;
  }

  String _getIngredientName() {
    return (context.locale == const Locale('ar') ? widget.ingredient.arabicName : widget.ingredient.englishName) ?? "Unknown Ingredient";
  }

  String _getAmountUnit() {
    return widget.ingredient.unit ?? 'units';
  }

  void _formatValue() {
    final value = double.tryParse(_amountController.text);
    if (value != null) {
      _amountController.text = value.toInt().toString();
    }
  }

  void _saveChanges() async {
    if (!_hasChanges) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newAmount = int.tryParse(_amountController.text) ?? 1;

      // Calculate the original total nutrition values for this ingredient
      final originalTotalCalories = widget.ingredient.calories ?? 0;
      final originalTotalProtein = widget.ingredient.protein ?? 0.0;
      final originalTotalCarbs = widget.ingredient.carbs ?? 0.0;
      final originalTotalFat = widget.ingredient.fat ?? 0.0;

      // Calculate the new total nutrition values for this ingredient
      var newTotalCalories = _calculatedCalories;
      var newTotalProtein = _calculatedProtein;
      var newTotalCarbs = _calculatedCarbs;
      var newTotalFat = _calculatedFat;

      // Calculate the difference to apply to the food model
      final ingredientCaloriesDiff = newTotalCalories - originalTotalCalories;
      final ingredientProteinDiff = newTotalProtein - originalTotalProtein;
      final ingredientCarbsDiff = newTotalCarbs - originalTotalCarbs;
      final ingredientFatDiff = newTotalFat - originalTotalFat;

      final updatedIngredient = Ingredient(
        englishName: widget.ingredient.englishName,
        arabicName: widget.ingredient.arabicName,
        calories: newTotalCalories,
        protein: newTotalProtein,
        carbs: newTotalCarbs,
        fat: newTotalFat,
        isHalal: widget.ingredient.isHalal,
        id: widget.ingredient.id,
        serving: widget.ingredient.serving,
        unit: widget.ingredient.unit,
        amount: newAmount,
      );

      final updatedIngredientList = List<Ingredient>.from(widget.foodModel.ingredientList);
      updatedIngredientList[widget.ingredientIndex] = updatedIngredient;

      newTotalCalories = (widget.foodModel.calories ?? 0) + ingredientCaloriesDiff;
      newTotalProtein = (widget.foodModel.protein ?? 0.0) + ingredientProteinDiff;
      newTotalCarbs = (widget.foodModel.carbs ?? 0.0) + ingredientCarbsDiff;
      newTotalFat = (widget.foodModel.fat ?? 0.0) + ingredientFatDiff;

      final updatedFoodModel = widget.foodModel.copyWith(
        ingredientList: updatedIngredientList,
        calories: newTotalCalories.round(),
        protein: newTotalProtein,
        carbs: newTotalCarbs,
        fat: newTotalFat,
      );

      log('EditIngredientAmountScreen: Updated ingredient amount from ${widget.ingredient.amount} to $newAmount');
      log('EditIngredientAmountScreen: Nutrition differences - Calories: $ingredientCaloriesDiff, Protein: $ingredientProteinDiff, Carbs: $ingredientCarbsDiff, Fat: $ingredientFatDiff');

      context.read<RecentActivityBloc>().add(UpdateFoodWithDifferences(
            meal: updatedFoodModel,
            caloriesDiff: ingredientCaloriesDiff.toDouble(),
            carbsDiff: ingredientCarbsDiff,
            proteinDiff: ingredientProteinDiff,
            fatDiff: ingredientFatDiff,
          ));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }

    context.pop();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: context.background,
      appBar: AppBar(
        forceMaterialTransparency: true,
        backgroundColor: context.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: context.onSecondary),
          onPressed: () => context.pop(),
        ),
        title: Text(
          LocaleKeys.home_edit_ingredient_amount.tr(),
          style: context.textTheme.titleLarge!.copyWith(
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Ingredient name
              Text(
                _getIngredientName(),
                style: context.textTheme.headlineSmall!.copyWith(
                  color: context.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
      
              const SizedBox(height: 20),
      
              // Amount input field
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: context.onPrimaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(25),
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: _amountController,
                  focusNode: _focusNode,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  style: context.textTheme.headlineMedium!.copyWith(
                    color: context.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  onFieldSubmitted: (_) => _formatValue(),
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(
                        color: context.primaryColor,
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 20,
                    ),
                    suffixText: _getAmountUnit(),
                    suffixStyle: context.textTheme.titleLarge!.copyWith(
                      color: context.onSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                    hintText: 'أدخل الكمية',
                    hintStyle: context.textTheme.titleMedium!.copyWith(
                      color: context.onSecondary.withAlpha(100),
                    ),
                  ),
                ),
              ),
      
              const SizedBox(height: 30),
      
              // Nutrition preview
              Text(
                LocaleKeys.home_nutrition_preview.tr(),
                style: context.textTheme.titleMedium!.copyWith(
                  color: context.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
      
              const SizedBox(height: 15),
      
              // Nutrition values display
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: context.onPrimaryColor,
                  border: Border.all(
                    color: context.onSecondary.withAlpha(29),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    _buildNutritionRow(
                      LocaleKeys.home_calories.tr(),
                      _calculatedCalories.toString(),
                      LocaleKeys.home_calorie.tr(),
                      context.primaryColor,
                    ),
                    const Divider(height: 20),
                    _buildNutritionRow(
                      LocaleKeys.home_protein.tr(),
                      _calculatedProtein.toStringAsFixed(1),
                      LocaleKeys.home_grams.tr(),
                      const Color(0xFFE55B35),
                    ),
                    const Divider(height: 20),
                    _buildNutritionRow(
                      LocaleKeys.home_carbs.tr(),
                      _calculatedCarbs.toStringAsFixed(1),
                      LocaleKeys.home_grams.tr(),
                      const Color(0xFFFFA76E),
                    ),
                    const Divider(height: 20),
                    _buildNutritionRow(
                      LocaleKeys.home_fats.tr(),
                      _calculatedFat.toStringAsFixed(1),
                      LocaleKeys.home_grams.tr(),
                      const Color(0xFF4277ff),
                    ),
                  ],
                ),
              ),
      
              const Spacer(),
      
              // Save button (full width)
              if (_hasChanges)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveChanges,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            LocaleKeys.home_save_changes.tr(),
                            style: context.textTheme.titleMedium!.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutritionRow(String label, String value, String unit, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: context.textTheme.bodyLarge!.copyWith(
            color: context.onSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: context.textTheme.titleMedium!.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              unit,
              style: context.textTheme.bodyMedium!.copyWith(
                color: context.onSecondary.withAlpha(150),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
