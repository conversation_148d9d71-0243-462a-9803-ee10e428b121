import 'dart:convert';

import 'package:catcher_2/catcher_2.dart';
import 'package:catcher_2/model/platform_type.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import './app.dart';

import './common/utils/init_main.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Initialization.initMain();
  Catcher2Options debugOptions = Catcher2Options(
    SilentReportMode(),
    [
      DiscordHandler(endpoint: 'http://82.112.250.50:5003/log/med'),
      Con<PERSON>e<PERSON><PERSON><PERSON>(),
      CustomTelegramHandler(
        botToken: '**********************************************',
        chatId: '248404801',
      )
    ],
    // Enable detailed logging
    logger: Catcher2Logger(),
    // Include device parameters in reports
    reportOccurrenceTimeout: 10000,
    // Custom report fields
    customParameters: {
      'custom_field': 'custom_value',
    },
    // Enable filtering if needed
    filterFunction: (Report report) {
      // Return true to send report, false to filter out
      return true;
    },
  );

  Catcher2Options releaseOptions = Catcher2Options(
    SilentReportMode(),
    [
      DiscordHandler(endpoint: 'http://82.112.250.50:5003/log/med'),
      CustomTelegramHandler(
        botToken: '**********************************************',
        chatId: '248404801',
      )
    ],
    // Enable detailed logging
    logger: Catcher2Logger(),
    // Include device parameters in reports
    reportOccurrenceTimeout: 10000,
    // Custom report fields
    customParameters: {
      'environment': 'production',
    },
    // Enable filtering if needed
    filterFunction: (Report report) {
      // Return true to send report, false to filter out
      return true;
    },
  );

  Catcher2(debugConfig: debugOptions, releaseConfig: releaseOptions, rootWidget: Initialization.initLocalization(const App()));
  // runApp(Initialization.initLocalization(const App()));

  // if (Platform.isWindows) {
  //   await windowManager.ensureInitialized();

  //   WindowOptions windowOptions = const WindowOptions(
  //     size: Size(400, 720),
  //     minimumSize: Size(400, 720),
  //     center: true,
  //     backgroundColor: Colors.transparent,
  //     skipTaskbar: false,
  //   );
  //   windowManager.waitUntilReadyToShow(windowOptions, () async {
  //     await windowManager.show();
  //     await windowManager.focus();
  //   });
  // }
}

class DiscordHandler extends ReportHandler {
  final String endpoint;
  final Dio _dio;

  DiscordHandler({required this.endpoint}) : _dio = Dio(BaseOptions(headers: {"Content-Type": "application/json"}));

  Future<bool> handleReport(Report report, BuildContext? context) async {
    final payload = {
      "team": "mobile",
      "level": "ERROR",
      "message": "${report.error}",
      "datetime": DateTime.now().toString(),
      "environment": "stage",
      "context": {
        "service": "app",
        "exception": {
          "stack": "${report.stackTrace}",
          "file": report.screenshot,
          "line": 0,
          "column": 0,
        }
      }
    };
    try {
      final response = await _dio.post(endpoint, data: jsonEncode(payload));
      return response.statusCode == 200;
    } catch (e) {
      debugPrint("Error sending to Discord handler: $e");
      return false;
    }
  }

  @override
  List<PlatformType> getSupportedPlatforms() {
    return [PlatformType.iOS, PlatformType.android];
  }

  @override
  Future<bool> handle(Report report, BuildContext? context) async {
    return handleReport(report, context);
  }
}

class CustomTelegramHandler extends ReportHandler {
  final String botToken;
  final String chatId;
  final Dio _dio;

  CustomTelegramHandler({required this.botToken, required this.chatId})
      : _dio = Dio(BaseOptions(headers: {"Content-Type": "application/json"}));

  Future<bool> handleReport(Report report, BuildContext? context) async {
    final payload = {
      "team": "mobile",
      "level": "ERROR",
      "message": "${report.error}",
      "datetime": DateTime.now().toString(),
      "environment": "stage",
      "context": {
        "service": "app",
        "exception": {
          "stack": "${report.stackTrace}",
          "file": report.screenshot,
          "line": 0,
          "column": 0,
        },
        "device": report.deviceParameters,
        "application": report.applicationParameters,
        "custom": report.customParameters,
        "platform": report.platformType.toString(),
      }
    };

    final url = 'https://api.telegram.org/bot$botToken/sendMessage';

    try {
      final response = await _dio.post(url, data: {
        'chat_id': chatId,
        'text': jsonEncode(payload),
      });
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('CustomTelegramHandler failed: $e');
      return false;
    }
  }

  @override
  List<PlatformType> getSupportedPlatforms() {
    return PlatformType.values;
  }

  @override
  Future<bool> handle(Report report, BuildContext? context) async {
    return handleReport(report, context);
  }
}
