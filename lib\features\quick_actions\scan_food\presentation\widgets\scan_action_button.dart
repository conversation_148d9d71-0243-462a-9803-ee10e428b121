// lib/features/quick_actions/scan_food/presentation/widgets/scan_action_button.dart

import 'package:flutter/material.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';

class ScanActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const ScanActionButton({
    super.key,
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppGestureDetector(
      onTap: onTap,
      child: Opacity(
        opacity: isSelected ? 1.0 : 0.7,
        child: Container(
          height: 75,
          width: 80,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? context.primaryColor : context.primaryColor.withAlpha(130),
              ),
              const SizedBox(height: 6),
              Text(
                textAlign: TextAlign.center,
                label,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                style: context.textTheme.labelSmall!.copyWith(
                  color: isSelected ? context.onSecondary : context.onSecondary.withAlpha(130),
                  fontSize: 9,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
