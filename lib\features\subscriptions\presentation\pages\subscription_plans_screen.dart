// COMMENTED OUT - EXISTING SUBSCRIPTION FEATURE
// // ignore_for_file: deprecated_member_use

// import 'package:cal/features/authentication/presentation/pages/authentication_page.dart';
// import 'package:cal/features/subscriptions/presentation/subscription_bloc/subscription_bloc.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:in_app_purchase/in_app_purchase.dart';

// class SubscriptionPlansScreen extends StatefulWidget {
//   const SubscriptionPlansScreen({super.key});

//   @override
//   State<SubscriptionPlansScreen> createState() => _SubscriptionPlansScreenState();
// }

// class _SubscriptionPlansScreenState extends State<SubscriptionPlansScreen> {
//   @override
//   void initState() {
//     super.initState();
//     // Load products when screen opens
//     context.read<SubscriptionBloc>().add(LoadProducts());
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.grey[50],
//       appBar: AppBar(
//         title: const Text('Choose Your Plan'),
//         backgroundColor: Colors.white,
//         foregroundColor: Colors.black87,
//         elevation: 0,
//         actions: [
//           TextButton(
//             onPressed: () {
//               context.read<SubscriptionBloc>().add(RestorePurchases());
//             },
//             child: const Text(
//               'Restore',
//               style: TextStyle(
//                 color: Colors.blue,
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ),
//         ],
//       ),
//       body: BlocConsumer<SubscriptionBloc, SubscriptionState>(
//         listener: (context, state) {
//           if (state.status == SubscriptionStatus.purchaseSuccess) {
//             // Navigate to success screen
//             Navigator.of(context).pushReplacement(
//               MaterialPageRoute(builder: (context) => const AuthenticationPage()),
//             );
//           } else if (state.status == SubscriptionStatus.error) {
//             _showErrorDialog(context, state.errorMessage ?? 'Unknown error');
//           } else if (state.status == SubscriptionStatus.restoreSuccess) {
//             Navigator.of(context).pushReplacement(
//               MaterialPageRoute(builder: (context) => const AuthenticationPage()),
//             );
//           }
//         },
//         builder: (context, state) {
//           if (state.isLoading) {
//             return const Center(
//               child: CircularProgressIndicator(),
//             );
//           }

//           if (state.hasError) {
//             return Center(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   Icon(
//                     Icons.error_outline,
//                     size: 64,
//                     color: Colors.grey[400],
//                   ),
//                   const SizedBox(height: 16),
//                   Text(
//                     'Something went wrong',
//                     style: Theme.of(context).textTheme.headlineSmall,
//                   ),
//                   const SizedBox(height: 8),
//                   Text(
//                     state.errorMessage ?? 'Unknown error',
//                     textAlign: TextAlign.center,
//                     style: TextStyle(color: Colors.grey[600]),
//                   ),
//                   const SizedBox(height: 24),
//                   ElevatedButton(
//                     onPressed: () {
//                       context.read<SubscriptionBloc>().add(LoadProducts());
//                     },
//                     child: const Text('Try Again'),
//                   ),
//                 ],
//               ),
//             );
//           }

//           if (state.hasProducts || state.isPurchasing || state.isValidating) {
//             return _buildProductsList(context, state.products, state);
//           }

//           return const Center(
//             child: Text('Welcome! Loading subscription plans...'),
//           );
//         },
//       ),
//     );
//   }

//   Widget _buildProductsList(BuildContext context, List<ProductDetails> products, SubscriptionState currentState) {
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Header
//           Text(
//             'Unlock Premium Features',
//             style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                   fontWeight: FontWeight.bold,
//                   color: Colors.black87,
//                 ),
//           ),
//           const SizedBox(height: 8),
//           Text(
//             'Choose the plan that works best for you',
//             style: TextStyle(
//               fontSize: 16,
//               color: Colors.grey[600],
//             ),
//           ),
//           const SizedBox(height: 32),

//           // Products list
//           ...products
//               .map((product) => _buildProductCard(
//                     context,
//                     product,
//                     currentState,
//                   ))
//               .toList(),

//           const SizedBox(height: 24),

//           // Features list
//           _buildFeaturesList(),

//           const SizedBox(height: 32),
//         ],
//       ),
//     );
//   }

/*
//   Widget _buildProductCard(BuildContext context, ProductDetails product, SubscriptionState currentState) {
    final isMonthly = product.id.contains('monthly');
    final isPopular = !isMonthly; // Make yearly popular
    final isPurchasing = currentState.isPurchasing && currentState.productId == product.id;
    final isValidating = currentState.isValidating && currentState.productId == product.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isPopular ? Colors.blue : Colors.grey[300]!,
                width: isPopular ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: isPurchasing || isValidating
                    ? null
                    : () {
                        context.read<SubscriptionBloc>().add(PurchaseProduct(product.id));
                      },
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isMonthly ? 'Monthly Plan' : 'Yearly Plan',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  isMonthly ? 'Billed monthly' : 'Billed annually',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                product.price,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Purchase button
                      SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: isPurchasing || isValidating
                              ? null
                              : () {
                                  context.read<SubscriptionBloc>().add(PurchaseProduct(product.id));
                                },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isPopular ? Colors.blue : Colors.grey[800],
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: isPurchasing
                              ? const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Text('Processing...'),
                                  ],
                                )
                              : isValidating
                                  ? const Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Text('Validating...'),
                                      ],
                                    )
                                  : Text(
                                      'Start ${isMonthly ? 'Monthly' : 'Yearly'} Plan',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Popular badge
          if (isPopular)
            Positioned(
              top: -1,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'POPULAR',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      'Unlimited access to all content',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What\'s included:',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
        ),
        const SizedBox(height: 16),
        ...features
            .map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          feature,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ],
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Purchase Failed'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
*/
