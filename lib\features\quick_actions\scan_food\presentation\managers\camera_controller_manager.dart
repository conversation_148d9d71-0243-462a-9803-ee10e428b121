
import 'dart:developer';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraControllerManager {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _hasPermission = false;
  bool _isFlashOn = false;
  bool _isDisposing = false;

  // Getters
  CameraController? get controller => _controller;
  bool get isInitialized => _isInitialized;
  bool get hasPermission => _hasPermission;
  bool get isFlashOn => _isFlashOn;
  bool get isDisposing => _isDisposing;

  Future<bool> checkAndRequestPermission() async {
    final status = await Permission.camera.status;
    log("Camera permission status: $status");

    if (status.isGranted) {
      _hasPermission = true;
      return true;
    } else if (status.isDenied || status.isRestricted) {
      log("Requesting camera permission...");
      final requestStatus = await Permission.camera.request();
      log("Camera permission request result: $requestStatus");
      _hasPermission = requestStatus.isGranted;
      return _hasPermission;
    } else if (status.isPermanentlyDenied) {
      log("Camera permission permanently denied, opening app settings");
      await openAppSettings();
      return false;
    }
    return false;
  }

  Future<String?> initializeCamera() async {
    if (_controller != null || _isDisposing) {
      log("Camera already initializing or disposing, skipping...");
      return null;
    }

    try {
      log("Initializing camera...");
      final cameras = await availableCameras();
      log("Available cameras: ${cameras.length}");

      if (cameras.isEmpty) {
        log("No cameras available");
        return 'No cameras available';
      }

      log("Creating camera controller...");
      final controller = CameraController(
        cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      log("Initializing camera controller...");
      await controller.initialize();
      log("Camera controller initialized successfully");

      if (!_isDisposing) {
        _controller = controller;
        _isInitialized = true;
        log("Camera controller set successfully");
        return null; // Success
      } else {
        log("Widget disposed during initialization, cleaning up controller");
        await controller.dispose();
        return 'Initialization cancelled';
      }
    } catch (e) {
      log("Camera initialization error: $e");
      return 'Failed to initialize camera: $e';
    }
  }

  Future<void> disposeCamera() async {
    if (_controller == null) return;

    log("Disposing camera controller...");
    _isDisposing = true;
    
    try {
      final controller = _controller;
      _controller = null;
      _isInitialized = false;
      _isFlashOn = false;

      await controller?.dispose();
      log("Camera controller disposed successfully");
    } catch (e) {
      log("Error disposing camera: $e");
    } finally {
      _isDisposing = false;
    }
  }

  Future<bool> toggleFlash() async {
    if (_controller != null && _controller!.value.isInitialized) {
      try {
        final newFlashMode = _isFlashOn ? FlashMode.off : FlashMode.torch;
        await _controller!.setFlashMode(newFlashMode);
        _isFlashOn = !_isFlashOn;
        return true;
      } catch (e) {
        log("Error toggling flash: $e");
        return false;
      }
    }
    return false;
  }

  Future<File?> captureImage() async {
    if (_controller == null || !_controller!.value.isInitialized || _isDisposing) {
      log("Cannot capture image: controller not ready or disposing");
      return null;
    }

    try {
      log("Capturing image...");
      final XFile image = await _controller!.takePicture();
      log("Image captured successfully");
      return File(image.path);
    } catch (e) {
      log("Failed to capture image: $e");
      rethrow;
    }
  }

  void markAsDisposing() {
    _isDisposing = true;
  }
}