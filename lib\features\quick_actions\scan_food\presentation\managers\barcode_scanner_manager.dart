// lib/features/quick_actions/scan_food/presentation/managers/barcode_scanner_manager.dart

import 'dart:developer';
import 'dart:io';
import 'package:cal/features/quick_actions/scan_food/presentation/managers/camera_controller_manager.dart';
import '../../../../../common/utils/google_ml_kit_scanner_helper.dart';

class BarcodeScannerManager {
  bool _isScanning = false;
  bool _shouldStopScanning = false;

  bool get isScanning => _isScanning;

  Future<void> startContinuousBarcodeScan({
    required CameraControllerManager cameraManager,
    required Function(String barcode, File imageFile) onBarcodeDetected,
    required Function() onScanningStateChanged,
  }) async {
    if (cameraManager.controller == null || !cameraManager.controller!.value.isInitialized || cameraManager.isDisposing || _isScanning) {
      return;
    }

    _isScanning = true;
    _shouldStopScanning = false;
    onScanningStateChanged();

    while (!_shouldStopScanning &&
        cameraManager.controller != null &&
        cameraManager.controller!.value.isInitialized &&
        !cameraManager.isDisposing) {
      try {
        final imageFile = await cameraManager.captureImage();
        if (imageFile == null) break;

        // Scan for barcode
        final scanner = BarcodeScannerService();
        final barcode = await scanner.scanFromFile(imageFile);
        scanner.dispose();

        if (barcode != null && !_shouldStopScanning && !cameraManager.isDisposing) {
          _isScanning = false;
          onScanningStateChanged();
          onBarcodeDetected(barcode, imageFile);
          return;
        }

        // Wait a bit before next scan to avoid excessive processing
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        log("Error during continuous barcode scan: $e");
        await Future.delayed(const Duration(milliseconds: 1000));
      }
    }

    _isScanning = false;
    onScanningStateChanged();
  }

  Future<String?> scanSingleImage(File imageFile) async {
    try {
      final scanner = BarcodeScannerService();
      final barcode = await scanner.scanFromFile(imageFile);
      scanner.dispose();
      return barcode;
    } catch (e) {
      log("Error scanning image for barcode: $e");
      return null;
    }
  }

  void stopScanning() {
    _shouldStopScanning = true;
    _isScanning = false;
  }
}
