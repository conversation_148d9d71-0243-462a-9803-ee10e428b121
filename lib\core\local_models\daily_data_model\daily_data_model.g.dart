// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'daily_data_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetDailyUserDataModelCollection on Isar {
  IsarCollection<DailyUserDataModel> get dailyUserDataModels =>
      this.collection();
}

const DailyUserDataModelSchema = CollectionSchema(
  name: r'DailyUserDataModel',
  id: 7221664449062964925,
  properties: {
    r'bmi': PropertySchema(
      id: 0,
      name: r'bmi',
      type: IsarType.double,
    ),
    r'burnedCalories': PropertySchema(
      id: 1,
      name: r'burnedCalories',
      type: IsarType.double,
    ),
    r'consumedCalories': PropertySchema(
      id: 2,
      name: r'consumedCalories',
      type: IsarType.double,
    ),
    r'consumedCarbs': PropertySchema(
      id: 3,
      name: r'consumedCarbs',
      type: IsarType.double,
    ),
    r'consumedFat': PropertySchema(
      id: 4,
      name: r'consumedFat',
      type: IsarType.double,
    ),
    r'consumedProtein': PropertySchema(
      id: 5,
      name: r'consumedProtein',
      type: IsarType.double,
    ),
    r'date': PropertySchema(
      id: 6,
      name: r'date',
      type: IsarType.dateTime,
    ),
    r'height': PropertySchema(
      id: 7,
      name: r'height',
      type: IsarType.double,
    ),
    r'sum': PropertySchema(
      id: 8,
      name: r'sum',
      type: IsarType.double,
    ),
    r'targetCalories': PropertySchema(
      id: 9,
      name: r'targetCalories',
      type: IsarType.double,
    ),
    r'targetCarbs': PropertySchema(
      id: 10,
      name: r'targetCarbs',
      type: IsarType.double,
    ),
    r'targetFat': PropertySchema(
      id: 11,
      name: r'targetFat',
      type: IsarType.double,
    ),
    r'targetProtein': PropertySchema(
      id: 12,
      name: r'targetProtein',
      type: IsarType.double,
    ),
    r'weight': PropertySchema(
      id: 13,
      name: r'weight',
      type: IsarType.double,
    )
  },
  estimateSize: _dailyUserDataModelEstimateSize,
  serialize: _dailyUserDataModelSerialize,
  deserialize: _dailyUserDataModelDeserialize,
  deserializeProp: _dailyUserDataModelDeserializeProp,
  idName: r'id',
  indexes: {
    r'date': IndexSchema(
      id: -7552997827385218417,
      name: r'date',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'date',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _dailyUserDataModelGetId,
  getLinks: _dailyUserDataModelGetLinks,
  attach: _dailyUserDataModelAttach,
  version: '3.1.0+1',
);

int _dailyUserDataModelEstimateSize(
  DailyUserDataModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  return bytesCount;
}

void _dailyUserDataModelSerialize(
  DailyUserDataModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.bmi);
  writer.writeDouble(offsets[1], object.burnedCalories);
  writer.writeDouble(offsets[2], object.consumedCalories);
  writer.writeDouble(offsets[3], object.consumedCarbs);
  writer.writeDouble(offsets[4], object.consumedFat);
  writer.writeDouble(offsets[5], object.consumedProtein);
  writer.writeDateTime(offsets[6], object.date);
  writer.writeDouble(offsets[7], object.height);
  writer.writeDouble(offsets[8], object.sum);
  writer.writeDouble(offsets[9], object.targetCalories);
  writer.writeDouble(offsets[10], object.targetCarbs);
  writer.writeDouble(offsets[11], object.targetFat);
  writer.writeDouble(offsets[12], object.targetProtein);
  writer.writeDouble(offsets[13], object.weight);
}

DailyUserDataModel _dailyUserDataModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = DailyUserDataModel(
    bmi: reader.readDouble(offsets[0]),
    burnedCalories: reader.readDouble(offsets[1]),
    consumedCalories: reader.readDouble(offsets[2]),
    consumedCarbs: reader.readDouble(offsets[3]),
    consumedFat: reader.readDouble(offsets[4]),
    consumedProtein: reader.readDouble(offsets[5]),
    date: reader.readDateTime(offsets[6]),
    height: reader.readDouble(offsets[7]),
    id: id,
    targetCalories: reader.readDouble(offsets[9]),
    targetCarbs: reader.readDouble(offsets[10]),
    targetFat: reader.readDouble(offsets[11]),
    targetProtein: reader.readDouble(offsets[12]),
    weight: reader.readDouble(offsets[13]),
  );
  return object;
}

P _dailyUserDataModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDouble(offset)) as P;
    case 1:
      return (reader.readDouble(offset)) as P;
    case 2:
      return (reader.readDouble(offset)) as P;
    case 3:
      return (reader.readDouble(offset)) as P;
    case 4:
      return (reader.readDouble(offset)) as P;
    case 5:
      return (reader.readDouble(offset)) as P;
    case 6:
      return (reader.readDateTime(offset)) as P;
    case 7:
      return (reader.readDouble(offset)) as P;
    case 8:
      return (reader.readDouble(offset)) as P;
    case 9:
      return (reader.readDouble(offset)) as P;
    case 10:
      return (reader.readDouble(offset)) as P;
    case 11:
      return (reader.readDouble(offset)) as P;
    case 12:
      return (reader.readDouble(offset)) as P;
    case 13:
      return (reader.readDouble(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _dailyUserDataModelGetId(DailyUserDataModel object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _dailyUserDataModelGetLinks(
    DailyUserDataModel object) {
  return [];
}

void _dailyUserDataModelAttach(
    IsarCollection<dynamic> col, Id id, DailyUserDataModel object) {
  object.id = id;
}

extension DailyUserDataModelByIndex on IsarCollection<DailyUserDataModel> {
  Future<DailyUserDataModel?> getByDate(DateTime date) {
    return getByIndex(r'date', [date]);
  }

  DailyUserDataModel? getByDateSync(DateTime date) {
    return getByIndexSync(r'date', [date]);
  }

  Future<bool> deleteByDate(DateTime date) {
    return deleteByIndex(r'date', [date]);
  }

  bool deleteByDateSync(DateTime date) {
    return deleteByIndexSync(r'date', [date]);
  }

  Future<List<DailyUserDataModel?>> getAllByDate(List<DateTime> dateValues) {
    final values = dateValues.map((e) => [e]).toList();
    return getAllByIndex(r'date', values);
  }

  List<DailyUserDataModel?> getAllByDateSync(List<DateTime> dateValues) {
    final values = dateValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'date', values);
  }

  Future<int> deleteAllByDate(List<DateTime> dateValues) {
    final values = dateValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'date', values);
  }

  int deleteAllByDateSync(List<DateTime> dateValues) {
    final values = dateValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'date', values);
  }

  Future<Id> putByDate(DailyUserDataModel object) {
    return putByIndex(r'date', object);
  }

  Id putByDateSync(DailyUserDataModel object, {bool saveLinks = true}) {
    return putByIndexSync(r'date', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByDate(List<DailyUserDataModel> objects) {
    return putAllByIndex(r'date', objects);
  }

  List<Id> putAllByDateSync(List<DailyUserDataModel> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'date', objects, saveLinks: saveLinks);
  }
}

extension DailyUserDataModelQueryWhereSort
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QWhere> {
  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhere> anyDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'date'),
      );
    });
  }
}

extension DailyUserDataModelQueryWhere
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QWhereClause> {
  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      dateEqualTo(DateTime date) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'date',
        value: [date],
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      dateNotEqualTo(DateTime date) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [],
              upper: [date],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [date],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [date],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [],
              upper: [date],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      dateGreaterThan(
    DateTime date, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [date],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      dateLessThan(
    DateTime date, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [],
        upper: [date],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterWhereClause>
      dateBetween(
    DateTime lowerDate,
    DateTime upperDate, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [lowerDate],
        includeLower: includeLower,
        upper: [upperDate],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension DailyUserDataModelQueryFilter
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QFilterCondition> {
  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      bmiEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bmi',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      bmiGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bmi',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      bmiLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bmi',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      bmiBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bmi',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      burnedCaloriesEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'burnedCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      burnedCaloriesGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'burnedCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      burnedCaloriesLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'burnedCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      burnedCaloriesBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'burnedCalories',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCaloriesEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'consumedCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCaloriesGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'consumedCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCaloriesLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'consumedCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCaloriesBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'consumedCalories',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCarbsEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'consumedCarbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCarbsGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'consumedCarbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCarbsLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'consumedCarbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedCarbsBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'consumedCarbs',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedFatEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'consumedFat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedFatGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'consumedFat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedFatLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'consumedFat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedFatBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'consumedFat',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedProteinEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'consumedProtein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedProteinGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'consumedProtein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedProteinLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'consumedProtein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      consumedProteinBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'consumedProtein',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      dateEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      dateGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      dateLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      dateBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      heightEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'height',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      heightGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'height',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      heightLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'height',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      heightBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'height',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      sumEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sum',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      sumGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sum',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      sumLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sum',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      sumBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sum',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCaloriesEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCaloriesGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCaloriesLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetCalories',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCaloriesBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetCalories',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCarbsEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetCarbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCarbsGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetCarbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCarbsLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetCarbs',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetCarbsBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetCarbs',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetFatEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetFat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetFatGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetFat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetFatLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetFat',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetFatBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetFat',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetProteinEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'targetProtein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetProteinGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'targetProtein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetProteinLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'targetProtein',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      targetProteinBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'targetProtein',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      weightEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      weightGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      weightLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'weight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterFilterCondition>
      weightBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'weight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }
}

extension DailyUserDataModelQueryObject
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QFilterCondition> {}

extension DailyUserDataModelQueryLinks
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QFilterCondition> {}

extension DailyUserDataModelQuerySortBy
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QSortBy> {
  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByBmi() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bmi', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByBmiDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bmi', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByBurnedCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'burnedCalories', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByBurnedCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'burnedCalories', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCalories', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCalories', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCarbs', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCarbs', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedFat', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedFat', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedProtein', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByConsumedProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedProtein', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByHeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortBySum() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sum', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortBySumDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sum', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCalories', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCalories', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCarbs', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCarbs', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetFat', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetFat', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetProtein', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByTargetProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetProtein', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      sortByWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.desc);
    });
  }
}

extension DailyUserDataModelQuerySortThenBy
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QSortThenBy> {
  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByBmi() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bmi', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByBmiDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bmi', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByBurnedCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'burnedCalories', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByBurnedCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'burnedCalories', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCalories', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCalories', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCarbs', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedCarbs', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedFat', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedFat', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedProtein', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByConsumedProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'consumedProtein', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByHeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenBySum() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sum', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenBySumDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sum', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCalories', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetCaloriesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCalories', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCarbs', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetCarbsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetCarbs', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetFat', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetFatDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetFat', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetProtein', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByTargetProteinDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'targetProtein', Sort.desc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.asc);
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QAfterSortBy>
      thenByWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'weight', Sort.desc);
    });
  }
}

extension DailyUserDataModelQueryWhereDistinct
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct> {
  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByBmi() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bmi');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByBurnedCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'burnedCalories');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByConsumedCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'consumedCalories');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByConsumedCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'consumedCarbs');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByConsumedFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'consumedFat');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByConsumedProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'consumedProtein');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'height');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctBySum() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sum');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByTargetCalories() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetCalories');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByTargetCarbs() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetCarbs');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByTargetFat() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetFat');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByTargetProtein() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'targetProtein');
    });
  }

  QueryBuilder<DailyUserDataModel, DailyUserDataModel, QDistinct>
      distinctByWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'weight');
    });
  }
}

extension DailyUserDataModelQueryProperty
    on QueryBuilder<DailyUserDataModel, DailyUserDataModel, QQueryProperty> {
  QueryBuilder<DailyUserDataModel, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations> bmiProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bmi');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      burnedCaloriesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'burnedCalories');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      consumedCaloriesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'consumedCalories');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      consumedCarbsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'consumedCarbs');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      consumedFatProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'consumedFat');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      consumedProteinProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'consumedProtein');
    });
  }

  QueryBuilder<DailyUserDataModel, DateTime, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations> heightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'height');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations> sumProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sum');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      targetCaloriesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetCalories');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      targetCarbsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetCarbs');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      targetFatProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetFat');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations>
      targetProteinProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'targetProtein');
    });
  }

  QueryBuilder<DailyUserDataModel, double, QQueryOperations> weightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'weight');
    });
  }
}
